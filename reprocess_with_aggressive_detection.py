#!/usr/bin/env python3
"""
Reprocess All Subjects with Aggressive Deception Detection
==========================================================

Uses the updated aggressive submission_scroll.py to reanalyze all existing transcripts
and overwrite the JSON results with more comprehensive deception detection.

Usage:
    python reprocess_with_aggressive_detection.py --batch-dir output/batch_20250916_165800
"""

import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List
import re

# Add scripts to path for imports
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

try:
    from submission_scroll import build_all, parse_master, extract_session_claims, detect_aggressive_contradictions, infer_revealed_truth_aggressive
except ImportError as e:
    print(f"Error importing aggressive submission_scroll: {e}")
    sys.exit(1)


class AggressiveReprocessor:
    """Reprocesses all subjects with aggressive deception detection."""
    
    def __init__(self, batch_dir: Path):
        self.batch_dir = Path(batch_dir)
        
    def discover_subjects(self) -> List[str]:
        """Discover all processed subjects in batch directory."""
        subjects = []
        for item in self.batch_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # Check if it has transcript files
                txt_files = list(item.glob("*_MASTER_TRANSCRIPT.txt"))
                if txt_files:
                    subjects.append(item.name)
        return sorted(subjects)
    
    def reprocess_subject(self, subject: str) -> Dict[str, Any]:
        """Reprocess a single subject with aggressive detection."""
        subject_dir = self.batch_dir / subject
        
        # Find the master transcript file
        txt_files = list(subject_dir.glob("*_MASTER_TRANSCRIPT.txt"))
        if not txt_files:
            print(f"No transcript file found for {subject}")
            return {}
        
        transcript_file = txt_files[0]
        print(f"Reprocessing {subject} with aggressive detection...")
        
        try:
            # Parse the transcript using the aggressive parser
            raw_text = transcript_file.read_text(encoding="utf-8")
            
            # Extract session claims using aggressive patterns
            session_claims = {}
            
            # Split transcript into sessions
            sessions = re.split(r'^SESSION \d+:', raw_text, flags=re.MULTILINE)
            session_headers = re.findall(r'^SESSION \d+:[^\n]*', raw_text, flags=re.MULTILINE)
            
            if len(sessions) > 1 and session_headers:
                for i, (header, session_content) in enumerate(zip(session_headers, sessions[1:]), 1):
                    session_id = f"{subject}::session_{i}"
                    session_claims[session_id] = extract_session_claims(session_content)
            else:
                # Fallback: treat entire content as one session
                session_id = f"{subject}::full_transcript"
                session_claims[session_id] = extract_session_claims(raw_text)
            
            # Apply aggressive contradiction detection
            contradictions = detect_aggressive_contradictions(session_claims)
            
            # Infer revealed truth with aggressive analysis
            revealed_truth = infer_revealed_truth_aggressive(session_claims)
            
            # Create final result
            final_result = {
                "shadow_id": subject,
                "revealed_truth": revealed_truth,
                "deception_patterns": contradictions
            }
            
            # Save updated JSON file
            json_file = subject_dir / f"{subject}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(final_result, f, indent=2, ensure_ascii=False)
            
            # Save updated submission scroll
            scroll_file = subject_dir / f"{subject}_submission_scroll.txt"
            with open(scroll_file, 'w', encoding='utf-8') as f:
                f.write("SUBMISSION SCROLL (AGGRESSIVE ANALYSIS)\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Subject: {subject}\n\n")
                f.write("REVEALED TRUTH:\n")
                for k, v in revealed_truth.items():
                    f.write(f"  {k}: {v}\n")
                f.write("\nDECEPTION PATTERNS:\n")
                if contradictions:
                    for c in contradictions:
                        f.write(f"- lie_type: {c.get('lie_type')}\n")
                        f.write(f"  contradictory_claims: {c.get('contradictory_claims')}\n")
                        if 'evidence' in c:
                            f.write("  evidence:\n")
                            for e in c['evidence']:
                                f.write(f"    - {e}\n")
                        f.write("\n")
                else:
                    f.write("  None detected.\n")
                f.write("\nFULL JSON:\n")
                f.write(json.dumps(final_result, indent=2))
            
            print(f"  ✓ Updated {subject}: {len(contradictions)} deception patterns found")
            return final_result
            
        except Exception as e:
            print(f"  ✗ Error processing {subject}: {e}")
            return {"error": str(e)}
    
    def reprocess_all(self) -> Dict[str, Any]:
        """Reprocess all subjects with aggressive detection."""
        subjects = self.discover_subjects()
        
        if not subjects:
            print("No subjects found in batch directory")
            return {}
        
        print(f"Found {len(subjects)} subjects to reprocess: {subjects}")
        print("Applying aggressive deception detection...")
        
        results = {}
        total_contradictions = 0
        subjects_with_contradictions = 0
        
        for subject in subjects:
            result = self.reprocess_subject(subject)
            if result and "error" not in result:
                results[subject] = result
                contradictions_count = len(result.get("deception_patterns", []))
                total_contradictions += contradictions_count
                if contradictions_count > 0:
                    subjects_with_contradictions += 1
            else:
                results[subject] = result
        
        # Generate summary
        summary = {
            "total_subjects": len(subjects),
            "subjects_with_contradictions": subjects_with_contradictions,
            "total_contradictions": total_contradictions,
            "deception_rate": f"{subjects_with_contradictions/len(subjects)*100:.1f}%" if subjects else "0%"
        }
        
        print(f"\n=== AGGRESSIVE REPROCESSING COMPLETE ===")
        print(f"Total Subjects: {summary['total_subjects']}")
        print(f"Subjects with Contradictions: {summary['subjects_with_contradictions']}")
        print(f"Total Contradictions Found: {summary['total_contradictions']}")
        print(f"Deception Rate: {summary['deception_rate']}")
        
        # Save reprocessing summary
        summary_file = self.batch_dir / "aggressive_reprocessing_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": summary,
                "results": results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"Summary saved to: {summary_file}")
        
        return {
            "summary": summary,
            "results": results
        }


def main():
    parser = argparse.ArgumentParser(
        description="Reprocess all subjects with aggressive deception detection",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--batch-dir", required=True, 
                       help="Batch directory containing processed subjects")
    
    args = parser.parse_args()
    
    batch_dir = Path(args.batch_dir)
    if not batch_dir.exists():
        print(f"Batch directory does not exist: {batch_dir}")
        return 1
    
    # Initialize reprocessor
    reprocessor = AggressiveReprocessor(batch_dir)
    
    try:
        results = reprocessor.reprocess_all()
        
        if results:
            print("\nReprocessing completed successfully!")
            print("All JSON files have been updated with aggressive deception detection.")
        else:
            print("No subjects found to reprocess")
            return 1
            
    except Exception as e:
        print(f"Error during reprocessing: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
