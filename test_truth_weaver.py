#!/usr/bin/env python3
"""
Truth Weaver Test Suite
=======================

Test the Truth Weaver system with sample data and validate outputs.

Usage:
    python test_truth_weaver.py --quick-test
    python test_truth_weaver.py --full-test
    python test_truth_weaver.py --create-sample-data
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List
import tempfile
import shutil

# Add src and scripts to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

try:
    from truth_weaver import Truth<PERSON><PERSON>ver
    from validate_output import OutputValidator
except ImportError as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)


class TruthWeaverTester:
    """Test suite for Truth Weaver system."""
    
    def __init__(self):
        self.test_dir = Path("test_data")
        self.output_dir = Path("test_output")
        
    def create_sample_audio_data(self):
        """Create sample audio files and transcripts for testing."""
        print("Creating sample test data...")
        
        # Create test directories
        audio_dir = self.test_dir / "audio"
        audio_dir.mkdir(parents=True, exist_ok=True)
        
        # Sample transcript data that should trigger contradictions
        sample_transcripts = {
            "phoenix_2024_1": """
I've been programming for 6 years now. I've mastered Python completely and built incredible systems from the ground up. 
I led a team of five developers on a major machine learning project. The architecture was entirely my design.
""",
            "phoenix_2024_2": """
Actually, I think it's been more like 3 years of real experience. Still learning advanced concepts in Python.
Some of the work involved copying and modifying existing solutions. Stack Overflow was very helpful.
""",
            "phoenix_2024_3": """
I LED A TEAM OF FIVE! EIGHT MONTHS ON MACHINE LEARNING! THE WHOLE SYSTEM WAS MY ARCHITECTURE!
Python is my main language but I also know some JavaScript and SQL.
""",
            "phoenix_2024_4": """
I... I work alone mostly. Never been comfortable with managing people or leading teams.
Most of my projects are individual contributions. I prefer debugging existing code to writing from scratch.
""",
            "phoenix_2024_5": """
Just 2 months debugging this system... I'm not what they think I am. 
I mostly assemble other people's modules and stitch them together. It's like duct tape programming.
I've been doing this for maybe 2-3 years total, including internships.
"""
        }
        
        # Create sample audio files (empty MP3 files) and corresponding transcript files
        for filename, transcript in sample_transcripts.items():
            # Create empty MP3 file
            mp3_file = audio_dir / f"{filename}.mp3"
            mp3_file.write_bytes(b"")  # Empty file for testing
            
            # Create transcript sidecar file for stub backend
            txt_file = audio_dir / f"{filename}.txt"
            txt_file.write_text(transcript.strip(), encoding="utf-8")
        
        print(f"Created sample data in: {audio_dir}")
        return audio_dir
    
    def run_quick_test(self) -> bool:
        """Run a quick test with stub backend."""
        print("Running quick test with stub backend...")
        
        # Create sample data
        audio_dir = self.create_sample_audio_data()
        
        # Initialize Truth Weaver with stub backend
        weaver = TruthWeaver(audio_dir, self.output_dir, backend="stub")
        
        try:
            # Process the phoenix_2024 subject
            result = weaver.process_subject("phoenix_2024")
            
            if not result:
                print("✗ Quick test failed: No result generated")
                return False
            
            # Validate the result structure
            validator = OutputValidator()
            
            # Check if JSON file was created
            json_file = self.output_dir / "phoenix_2024.json"
            if not json_file.exists():
                print("✗ Quick test failed: JSON file not created")
                return False
            
            # Validate JSON file
            valid, errors, warnings = validator.validate_file(json_file)
            
            if not valid:
                print("✗ Quick test failed: Invalid JSON output")
                for error in errors:
                    print(f"  ERROR: {error}")
                return False
            
            # Check if transcript file was created
            txt_file = self.output_dir / "phoenix_2024_MASTER_TRANSCRIPT.txt"
            if not txt_file.exists():
                print("✗ Quick test failed: Transcript file not created")
                return False
            
            # Validate transcript file
            valid, errors, warnings = validator.validate_file(txt_file)
            
            if not valid:
                print("✗ Quick test failed: Invalid transcript output")
                for error in errors:
                    print(f"  ERROR: {error}")
                return False
            
            # Check for expected contradictions
            deception_patterns = result.get("deception_patterns", [])
            if len(deception_patterns) == 0:
                print("⚠ Quick test warning: No contradictions detected")
            else:
                print(f"✓ Found {len(deception_patterns)} deception patterns")
            
            # Print sample output
            print("\nSample Truth Weaver Output:")
            print("=" * 40)
            print(json.dumps(result, indent=2))
            
            print("✓ Quick test passed!")
            return True
            
        except Exception as e:
            print(f"✗ Quick test failed with exception: {e}")
            return False
    
    def run_full_test(self) -> bool:
        """Run full test with real audio files if available."""
        print("Running full test...")
        
        # Check if real audio files exist
        real_audio_dir = Path("audio copy/audio")
        if not real_audio_dir.exists():
            print("Real audio directory not found, falling back to quick test")
            return self.run_quick_test()
        
        # Find a subject to test with
        audio_files = list(real_audio_dir.glob("*.mp3"))
        if not audio_files:
            print("No audio files found, falling back to quick test")
            return self.run_quick_test()
        
        # Extract first subject name
        first_file = audio_files[0]
        import re
        match = re.match(r'^(.+?)_\d+\.mp3$', first_file.name)
        if not match:
            print("Could not extract subject name, falling back to quick test")
            return self.run_quick_test()
        
        subject = match.group(1)
        print(f"Testing with real subject: {subject}")
        
        # Initialize Truth Weaver with AssemblyAI backend
        weaver = TruthWeaver(real_audio_dir, self.output_dir, backend="assemblyai")
        
        try:
            # Process the subject
            result = weaver.process_subject(subject)
            
            if not result:
                print("✗ Full test failed: No result generated")
                return False
            
            # Validate outputs
            validator = OutputValidator()
            
            json_file = self.output_dir / f"{subject}.json"
            txt_file = self.output_dir / f"{subject}_MASTER_TRANSCRIPT.txt"
            
            json_valid, json_errors, json_warnings = validator.validate_file(json_file)
            txt_valid, txt_errors, txt_warnings = validator.validate_file(txt_file)
            
            if not json_valid:
                print("✗ Full test failed: Invalid JSON output")
                for error in json_errors:
                    print(f"  ERROR: {error}")
                return False
            
            if not txt_valid:
                print("✗ Full test failed: Invalid transcript output")
                for error in txt_errors:
                    print(f"  ERROR: {error}")
                return False
            
            print("✓ Full test passed!")
            print(f"✓ Processed subject: {subject}")
            print(f"✓ Generated valid JSON: {json_file}")
            print(f"✓ Generated valid transcript: {txt_file}")
            
            if json_warnings or txt_warnings:
                print("\nWarnings:")
                for warning in json_warnings + txt_warnings:
                    print(f"  ⚠ {warning}")
            
            return True
            
        except Exception as e:
            print(f"✗ Full test failed with exception: {e}")
            return False
    
    def cleanup_test_data(self):
        """Clean up test data and outputs."""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        print("Cleaned up test data")


def main():
    parser = argparse.ArgumentParser(
        description="Truth Weaver Test Suite",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--quick-test", action="store_true", 
                       help="Run quick test with stub backend")
    parser.add_argument("--full-test", action="store_true",
                       help="Run full test with real audio files")
    parser.add_argument("--create-sample-data", action="store_true",
                       help="Create sample test data only")
    parser.add_argument("--cleanup", action="store_true",
                       help="Clean up test data and outputs")
    
    args = parser.parse_args()
    
    if not any([args.quick_test, args.full_test, args.create_sample_data, args.cleanup]):
        parser.error("Must specify one of: --quick-test, --full-test, --create-sample-data, or --cleanup")
    
    tester = TruthWeaverTester()
    
    try:
        if args.cleanup:
            tester.cleanup_test_data()
            return 0
        
        if args.create_sample_data:
            tester.create_sample_audio_data()
            print("Sample data created successfully")
            return 0
        
        if args.quick_test:
            success = tester.run_quick_test()
            return 0 if success else 1
        
        if args.full_test:
            success = tester.run_full_test()
            return 0 if success else 1
    
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        return 1
    except Exception as e:
        print(f"Test failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
