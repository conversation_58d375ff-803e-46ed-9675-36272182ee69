atlasmp: im a seasoned devops engineer specializing in kubernetes for the past year ive been in the trenches managing our production clusters personally responsible for our entire networking and security posture

atlasmp: why calico ha what else would you use its the only serious choice for network policy enforcement at scale i wrote all our policies from scratch to ensure total service isolation

atlasmp: i id check the logs then maybe the core dns logs id probably just restart the pod that usually fixes things

atlasmp: the senior engineer usually handles the network debugging i just deploy the yaml files he gives me

atlasmp: okay it was an internship it was a summer internship and i mostly just watched the senior engineers work i ran some scripts they gave me im not a devops engineer i just want to be one

criusmp: okay lead engineer might be the wrong term i was a developer on the e commerce team my specific assigned area of responsibility was that small coupon code component

eosmp: im a principal cware engineer for eight years ive been dedicated to the craft of building clean scalable and maintainable backend systems my passion is taking a complex business problem and translating it into elegant code

eosmp: the most challenging problem was a race condition in our caching layer that only appeared under heavy load once a month i spent a week instrumenting the code and finally caught it the fix was one line but the hunt was exhilarating thats the kind of work i love

eosmp: ah yes regarding the architected the entire solution part on my resume i should correct that wording architected is too strong i was the architect of a specific component the data ingestion service within a larger architecture designed by our team lead

eosmp: within my service i chose cosmos db for its turnkey geo replication even though it was more expensive i wrote a detailed design document justifying the cost by showing it would save us three months of engineering effort trying to build our own solution its about trade offs

eosmp: while im proud of my service level design skills my goal is to grow into that lead architect role i want to learn more about the challenges of designing the seams between the services not just the services themselves

hyperionmp: when i started there was nothing but an idea i built my backend from the very first line of code every django model every celery task every database migration that was all me its my creation

hyperionmp: the most complex part taming celery anyone whos worked with it knows its a beast im the one who figured out how to make it reliable for our background job processing

hyperionmp: i the lead dev actually wrote a library for that i just used his transaction decorator

hyperionmp: i wrote all the business logic he just provided some some infrastructure shell it was my implementation

hyperionmp: look fine he designed the core architecture and the database schema i wrote the code for it i was the lead developer not the architect it was my code but it was his system

oceanusmp: good morning im a c developer and manager with over a decade of experience primarily in the pressure cooker of low latency trading systems my teams build software that measures its response time in nanoseconds

oceanusmp: performance is everything we fight for every byte of memory and every clock cycle that means custom memory allocators kernel bypass networking and a deep almost obsessive understanding of cpu cache behavior

oceanusmp: as a manager of six my job is to shield the team from distractions and empower them to do their best work i handle the politics so they can handle the code i trust them implicitly and they trust me to have their backs

oceanusmp: my design philosophy simple fast and correct in that order we prototype quickly we measure relentlessly and we only add complexity when the data proves beyond a shadow of a doubt that its necessary

oceanusmp: im interested in the technical challenges your team is facing specifically are you dealing with issues related to inconsistent network jitter in your cloud environment and what strategies have you found most effective

rheamp: i live and breathe distributed systems for the past six years my obsession has been taming the chaos of asynchronous java services as a tech lead my job is to make them not just work but work with elegance and resilience

rheamp: you asked about kafka let me tell you about the am outage where a poison pill message brought our entire payment system to its knees it was a trial by fire but by sunrise we had not only fixed it but had re architected the consumer logic with a dead letter q to make sure it could never happen again i love that struggle

rheamp: idempotency isnt just a buzzword for us its a religion we enforce it at the consumer level with keys in redis but we also build it into our service apis its about building systems that expect to fail and can recover gracefully

rheamp: my proudest moment wasnt a feature i shipped it was watching a junior engineer i mentored who was terrified of public speaking give a department wide presentation on the benefits of our new microservices pattern my job is to build great engineers not just great software

rheamp: ive read your companys tech blog on your cloud migration you mentioned challenges with service discovery im intensely curious about the specific tradeoffs you debated when choosing between a full service mesh and a simpler client side library approach

selenemp: im a seasoned ruby on rails developer but my real passion is data im proficient across the modern data stack ai machine learning and big data im ready to build your next predictive engine

selenemp: lets talk modeling whether its a classification or regression problem im comfortable deploying models using frameworks like tensorflow or pytorch the entire pipeline from etl to inference is in my wheelhouse

selenemp: how would i handle class imbalance thats a classic youd use over sampling techniques like smote or you could use a different evaluation metric its a standard problem with a standard solution

selenemp: could i code a solution for that now well no not off the top of my head i typically work with a data scientist who handles that part my role is more about the big picture and the rails integration

selenemp: oh god hes asking more data questions i only took a weekend workshop on this stuff i dont know any of it just ask me about rails please just ask me about rails

titanmp: lets be clear for seven years i was the front end department the title was lead architect but in reality i was the visionary i designed and built our entire micro front end platform from scratch

titanmp: ha debate over the architecture there was no debate i made the right decisions react was the only logical choice for scalability anyone who suggested otherwise simply didnt understand the problem

titanmp: a custom hook seriously this is a pointless memorization exercise in a real environment my ide writes this boilerplate for me this is grunt work and its frankly insulting

titanmp: i architect systems not tiny functions are you questioning my entire career over a trivial piece of code

titanmp: oh god they know they know im a fraud im not an architect im a junior dev ive only been there two years i just wanted a better job
