#!/usr/bin/env python3
"""
Truth Weaver Output Validator
=============================

Validates that Truth Weaver outputs match the required format and structure.

Usage:
    python validate_output.py --json-file output/subject_01.json
    python validate_output.py --txt-file output/subject_01_MASTER_TRANSCRIPT.txt
    python validate_output.py --batch-dir output/batch_20241216_143022
"""

import json
import argparse
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import re


class OutputValidator:
    """Validates Truth Weaver outputs against required format."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def validate_json_structure(self, data: Dict[str, Any]) -> bool:
        """Validate JSON structure matches required format."""
        required_fields = {
            "shadow_id": str,
            "revealed_truth": dict,
            "deception_patterns": list
        }
        
        # Check top-level fields
        for field, expected_type in required_fields.items():
            if field not in data:
                self.errors.append(f"Missing required field: {field}")
                return False
            
            if not isinstance(data[field], expected_type):
                self.errors.append(f"Field '{field}' should be {expected_type.__name__}, got {type(data[field]).__name__}")
                return False
        
        # Validate revealed_truth structure
        revealed_truth = data["revealed_truth"]
        required_truth_fields = {
            "programming_experience": str,
            "programming_language": str,
            "skill_mastery": str,
            "leadership_claims": str,
            "team_experience": str,
            "skills and other keywords": list
        }
        
        for field, expected_type in required_truth_fields.items():
            if field not in revealed_truth:
                self.errors.append(f"Missing required field in revealed_truth: {field}")
            elif not isinstance(revealed_truth[field], expected_type):
                self.errors.append(f"Field 'revealed_truth.{field}' should be {expected_type.__name__}, got {type(revealed_truth[field]).__name__}")
        
        # Validate deception_patterns structure
        deception_patterns = data["deception_patterns"]
        for i, pattern in enumerate(deception_patterns):
            if not isinstance(pattern, dict):
                self.errors.append(f"deception_patterns[{i}] should be dict, got {type(pattern).__name__}")
                continue
            
            required_pattern_fields = ["lie_type", "contradictory_claims"]
            for field in required_pattern_fields:
                if field not in pattern:
                    self.errors.append(f"Missing required field in deception_patterns[{i}]: {field}")
        
        return len(self.errors) == 0
    
    def validate_json_content(self, data: Dict[str, Any]) -> bool:
        """Validate JSON content quality and completeness."""
        revealed_truth = data.get("revealed_truth", {})
        
        # Check for meaningful values (not just "unknown" or empty)
        meaningful_fields = 0
        total_fields = 6  # Total number of revealed_truth fields
        
        for field, value in revealed_truth.items():
            if field == "skills and other keywords":
                if isinstance(value, list) and len(value) > 0:
                    meaningful_fields += 1
            elif isinstance(value, str) and value not in ["unknown", "", "none", "null"]:
                meaningful_fields += 1
        
        completeness_ratio = meaningful_fields / total_fields
        if completeness_ratio < 0.5:
            self.warnings.append(f"Low completeness ratio: {completeness_ratio:.1%} of fields have meaningful values")
        
        # Check for contradictions
        deception_patterns = data.get("deception_patterns", [])
        if len(deception_patterns) == 0:
            self.warnings.append("No deception patterns detected - this may indicate insufficient analysis")
        
        # Validate shadow_id format
        shadow_id = data.get("shadow_id", "")
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', shadow_id):
            self.warnings.append(f"shadow_id '{shadow_id}' doesn't follow expected naming convention")
        
        return True
    
    def validate_transcript_format(self, content: str) -> bool:
        """Validate transcript format and structure."""
        lines = content.split('\n')
        
        # Check for session headers
        session_headers = [line for line in lines if re.match(r'^SESSION \d+:', line)]
        if len(session_headers) == 0:
            self.errors.append("No session headers found (expected format: 'SESSION 1: Title')")
            return False
        
        # Check for full text sections
        full_text_sections = [line for line in lines if line.strip() == "FULL TEXT:"]
        if len(full_text_sections) == 0:
            self.warnings.append("No 'FULL TEXT:' sections found")
        
        # Check for reasonable content length
        total_content = len(content.strip())
        if total_content < 100:
            self.warnings.append(f"Transcript seems very short ({total_content} characters)")
        elif total_content > 50000:
            self.warnings.append(f"Transcript seems very long ({total_content} characters)")
        
        # Check for confidence information
        confidence_lines = [line for line in lines if "Overall Confidence:" in line]
        if len(confidence_lines) == 0:
            self.warnings.append("No confidence information found")
        
        return True
    
    def validate_file(self, file_path: Path) -> Tuple[bool, List[str], List[str]]:
        """Validate a single file and return results."""
        self.errors = []
        self.warnings = []
        
        if not file_path.exists():
            self.errors.append(f"File does not exist: {file_path}")
            return False, self.errors, self.warnings
        
        try:
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                structure_valid = self.validate_json_structure(data)
                content_valid = self.validate_json_content(data)
                
                return structure_valid and content_valid, self.errors, self.warnings
            
            elif file_path.suffix.lower() == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                format_valid = self.validate_transcript_format(content)
                return format_valid, self.errors, self.warnings
            
            else:
                self.errors.append(f"Unsupported file type: {file_path.suffix}")
                return False, self.errors, self.warnings
                
        except json.JSONDecodeError as e:
            self.errors.append(f"Invalid JSON format: {e}")
            return False, self.errors, self.warnings
        except Exception as e:
            self.errors.append(f"Error reading file: {e}")
            return False, self.errors, self.warnings
    
    def validate_batch_directory(self, batch_dir: Path) -> Dict[str, Any]:
        """Validate all files in a batch directory."""
        if not batch_dir.exists():
            return {"error": f"Batch directory does not exist: {batch_dir}"}
        
        results = {
            "batch_dir": str(batch_dir),
            "subjects": {},
            "summary": {
                "total_subjects": 0,
                "valid_subjects": 0,
                "invalid_subjects": 0,
                "total_errors": 0,
                "total_warnings": 0
            }
        }
        
        # Find all subject directories
        subject_dirs = [d for d in batch_dir.iterdir() if d.is_dir() and not d.name.startswith('.')]
        
        for subject_dir in subject_dirs:
            subject_name = subject_dir.name
            subject_result = {
                "json_file": None,
                "txt_file": None,
                "valid": True,
                "errors": [],
                "warnings": []
            }
            
            # Look for JSON file
            json_files = list(subject_dir.glob("*.json"))
            if json_files:
                json_file = json_files[0]  # Take the first one
                valid, errors, warnings = self.validate_file(json_file)
                subject_result["json_file"] = str(json_file)
                subject_result["errors"].extend(errors)
                subject_result["warnings"].extend(warnings)
                if not valid:
                    subject_result["valid"] = False
            else:
                subject_result["errors"].append("No JSON file found")
                subject_result["valid"] = False
            
            # Look for transcript file
            txt_files = list(subject_dir.glob("*MASTER_TRANSCRIPT.txt"))
            if txt_files:
                txt_file = txt_files[0]  # Take the first one
                valid, errors, warnings = self.validate_file(txt_file)
                subject_result["txt_file"] = str(txt_file)
                subject_result["errors"].extend(errors)
                subject_result["warnings"].extend(warnings)
                if not valid:
                    subject_result["valid"] = False
            else:
                subject_result["errors"].append("No transcript file found")
                subject_result["valid"] = False
            
            results["subjects"][subject_name] = subject_result
            
            # Update summary
            results["summary"]["total_subjects"] += 1
            if subject_result["valid"]:
                results["summary"]["valid_subjects"] += 1
            else:
                results["summary"]["invalid_subjects"] += 1
            
            results["summary"]["total_errors"] += len(subject_result["errors"])
            results["summary"]["total_warnings"] += len(subject_result["warnings"])
        
        return results


def main():
    parser = argparse.ArgumentParser(
        description="Truth Weaver Output Validator",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--json-file", help="Validate a single JSON file")
    parser.add_argument("--txt-file", help="Validate a single transcript file")
    parser.add_argument("--batch-dir", help="Validate all files in a batch directory")
    parser.add_argument("--output", help="Save validation report to file")
    
    args = parser.parse_args()
    
    if not any([args.json_file, args.txt_file, args.batch_dir]):
        parser.error("Must specify one of: --json-file, --txt-file, or --batch-dir")
    
    validator = OutputValidator()
    
    try:
        if args.batch_dir:
            # Validate batch directory
            results = validator.validate_batch_directory(Path(args.batch_dir))
            
            print("BATCH VALIDATION RESULTS")
            print("=" * 50)
            print(f"Batch Directory: {results['batch_dir']}")
            print(f"Total Subjects: {results['summary']['total_subjects']}")
            print(f"Valid Subjects: {results['summary']['valid_subjects']}")
            print(f"Invalid Subjects: {results['summary']['invalid_subjects']}")
            print(f"Total Errors: {results['summary']['total_errors']}")
            print(f"Total Warnings: {results['summary']['total_warnings']}")
            print()
            
            # Show details for each subject
            for subject, result in results["subjects"].items():
                status = "✓ VALID" if result["valid"] else "✗ INVALID"
                print(f"{subject}: {status}")
                
                if result["errors"]:
                    for error in result["errors"]:
                        print(f"  ERROR: {error}")
                
                if result["warnings"]:
                    for warning in result["warnings"]:
                        print(f"  WARNING: {warning}")
                print()
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
                print(f"Detailed results saved to: {args.output}")
        
        else:
            # Validate single file
            file_path = Path(args.json_file or args.txt_file)
            valid, errors, warnings = validator.validate_file(file_path)
            
            print(f"VALIDATION RESULTS FOR: {file_path}")
            print("=" * 50)
            
            if valid:
                print("✓ VALID")
            else:
                print("✗ INVALID")
            
            if errors:
                print("\nERRORS:")
                for error in errors:
                    print(f"  - {error}")
            
            if warnings:
                print("\nWARNINGS:")
                for warning in warnings:
                    print(f"  - {warning}")
            
            if not errors and not warnings:
                print("No issues found!")
    
    except Exception as e:
        print(f"Error during validation: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
