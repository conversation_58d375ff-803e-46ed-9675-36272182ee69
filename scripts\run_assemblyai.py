# scripts/run_assemblyai.py
"""
AssemblyAI + stub ASR runner.

Provides:
- transcribe_file_assemblyai(file_path, options=None, poll_timeout=600.0)
- transcribe_file(file_path, backend='assemblyai'|'stub', **kwargs)   <-- the wrapper callers expect
"""

import os
import time
import requests
from pathlib import Path
from typing import Dict, Any, List

ASSEMBLYAI_ENDPOINT = "https://api.assemblyai.com/v2"

def _get_api_key() -> str:
    # First try to get from environment variable
    key = os.getenv("ASSEMBLYAI_API_KEY")
    if not key:
        # Fallback to hardcoded key (not recommended for production)
        key = "e73cfb30a1a9486bb8f2488172feaa12"
        print("Warning: Using hardcoded API key. Set ASSEMBLYAI_API_KEY environment variable for better security.")
    return key

def _upload_file_to_assemblyai(file_path: str) -> str:
    api_key = _get_api_key()
    headers = {"authorization": api_key}
    upload_url = f"{ASSEMBLYAI_ENDPOINT}/upload"
    with open(file_path, "rb") as fh:
        # streaming upload of bytes
        resp = requests.post(upload_url, headers=headers, data=fh)
    resp.raise_for_status()
    return resp.json()["upload_url"]

def _request_transcript(audio_url: str, options: Dict[str, Any] = None) -> str:
    api_key = _get_api_key()
    headers = {"authorization": api_key, "content-type": "application/json"}
    body = {"audio_url": audio_url}
    if options:
        body.update(options)
    resp = requests.post(f"{ASSEMBLYAI_ENDPOINT}/transcript", headers=headers, json=body)
    resp.raise_for_status()
    return resp.json()["id"]

def _poll_transcript(transcript_id: str, poll_interval: float = 2.0, timeout: float = 600.0) -> Dict[str, Any]:
    api_key = _get_api_key()
    headers = {"authorization": api_key}
    url = f"{ASSEMBLYAI_ENDPOINT}/transcript/{transcript_id}"
    start = time.time()
    backoff = poll_interval
    while True:
        resp = requests.get(url, headers=headers)
        resp.raise_for_status()
        data = resp.json()
        status = data.get("status")
        if status == "completed":
            return data
        if status == "error":
            raise RuntimeError(f"AssemblyAI transcription failed: {data.get('error')}")
        if time.time() - start > timeout:
            raise TimeoutError("Timed out waiting for AssemblyAI transcription.")
        time.sleep(backoff)
        backoff = min(backoff * 1.5, 10.0)

def _build_segments_from_result(result: Dict[str, Any]) -> List[Dict[str, Any]]:
    segments: List[Dict[str, Any]] = []

    # Prefer utterances (speaker-aware chunks) for better analysis
    if result.get("utterances"):
        for u in result["utterances"]:
            segments.append({
                "start": (u.get("start", 0) or 0) / 1000.0,
                "end": (u.get("end", 0) or 0) / 1000.0,
                "text": u.get("text", ""),
                "confidence": u.get("confidence", 0.0),
                "speaker": u.get("speaker", "A")  # Include speaker info
            })
    elif result.get("words"):
        # Group words into meaningful segments
        curr_words = []
        curr_start = None
        curr_end = None
        curr_confidence = []

        for w in result["words"]:
            if curr_start is None:
                curr_start = w.get("start")
            curr_end = w.get("end")
            curr_words.append(w.get("text", ""))
            curr_confidence.append(w.get("confidence", 0.0))

            # Break on sentence endings or after ~15 words for readability
            word_text = w.get("text", "")
            should_break = (
                len(curr_words) >= 15 or
                word_text.endswith((".", "?", "!")) or
                word_text.endswith(("...", "—"))
            )

            if should_break:
                avg_conf = sum(curr_confidence) / len(curr_confidence) if curr_confidence else 0.0
                segments.append({
                    "start": (curr_start or 0) / 1000.0,
                    "end": (curr_end or 0) / 1000.0,
                    "text": " ".join(curr_words),
                    "confidence": avg_conf,
                    "speaker": "A"  # Default speaker
                })
                curr_words = []
                curr_start = None
                curr_end = None
                curr_confidence = []

        # Handle remaining words
        if curr_words:
            avg_conf = sum(curr_confidence) / len(curr_confidence) if curr_confidence else 0.0
            segments.append({
                "start": (curr_start or 0) / 1000.0,
                "end": (curr_end or 0) / 1000.0,
                "text": " ".join(curr_words),
                "confidence": avg_conf,
                "speaker": "A"
            })
    else:
        # Fallback: single segment with full text
        segments = [{
            "start": 0.0,
            "end": 0.0,
            "text": result.get("text", ""),
            "confidence": result.get("confidence", 0.0),
            "speaker": "A"
        }]

    return segments

def transcribe_file_assemblyai(file_path: str, options: Dict[str, Any] = None, poll_timeout: float = 600.0) -> Dict[str, Any]:
    p = Path(file_path)
    if not p.exists():
        raise FileNotFoundError(file_path)

    # Default options for better transcription quality
    default_options = {
        "speaker_labels": True,  # Enable speaker diarization
        "auto_chapters": False,
        "sentiment_analysis": False,
        "entity_detection": False,
        "punctuate": True,
        "format_text": True
    }

    if options:
        default_options.update(options)

    upload_url = _upload_file_to_assemblyai(str(p))
    tid = _request_transcript(upload_url, options=default_options)
    result = _poll_transcript(tid, timeout=poll_timeout)
    segments = _build_segments_from_result(result)

    # Enhanced return with more metadata
    return {
        "raw_text": result.get("text", ""),
        "segments": segments,
        "raw_result": result,
        "audio_duration": result.get("audio_duration", 0) / 1000.0 if result.get("audio_duration") else 0,
        "confidence": result.get("confidence", 0.0),
        "speaker_count": len(set(u.get("speaker", "A") for u in result.get("utterances", []))) if result.get("utterances") else 1
    }

# Enhanced stub backend for offline testing
def transcribe_file_stub(file_path: str) -> Dict[str, Any]:
    p = Path(file_path)
    txt_sidecar = p.with_suffix(".txt")

    if txt_sidecar.exists():
        text = txt_sidecar.read_text(encoding="utf-8")
        # Split into segments for better analysis
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        segments = []

        for i, sentence in enumerate(sentences):
            if sentence:
                segments.append({
                    "start": i * 3.0,  # Simulate 3 seconds per sentence
                    "end": (i + 1) * 3.0,
                    "text": sentence + ".",
                    "confidence": 0.95,
                    "speaker": "A"
                })

        return {
            "raw_text": text,
            "segments": segments,
            "audio_duration": len(sentences) * 3.0,
            "confidence": 0.95,
            "speaker_count": 1
        }

    # Generate realistic stub content based on filename
    stub_text = f"This is a simulated transcript for {p.stem}. I have been working in programming for several years now. My experience includes various projects and technologies."

    return {
        "raw_text": stub_text,
        "segments": [{
            "start": 0.0,
            "end": 10.0,
            "text": stub_text,
            "confidence": 0.8,
            "speaker": "A"
        }],
        "audio_duration": 10.0,
        "confidence": 0.8,
        "speaker_count": 1
    }

def transcribe_file(file_path: str, backend: str = "assemblyai", **kwargs) -> Dict[str, Any]:
    """
    Generic wrapper used by the orchestrator.
    - backend: 'assemblyai' or 'stub'
    - kwargs: passed to the underlying function (e.g., options for assemblyai)
    """
    if backend == "stub":
        return transcribe_file_stub(file_path)
    elif backend == "assemblyai":
        return transcribe_file_assemblyai(file_path, options=kwargs.get("options"), poll_timeout=kwargs.get("poll_timeout", 600.0))
    else:
        raise ValueError("Unsupported backend: choose 'assemblyai' or 'stub'.")

if __name__ == "__main__":
    import sys
    import json

    if len(sys.argv) < 2:
        print("Usage: python run_assemblyai.py <audio_file_path> [backend]")
        print("  backend: 'assemblyai' (default) or 'stub'")
        print("Example: python run_assemblyai.py data/subject_01/audio.wav")
        sys.exit(1)

    file_path = sys.argv[1]
    backend = sys.argv[2] if len(sys.argv) > 2 else "assemblyai"

    print(f"Transcribing {file_path} using {backend} backend...")

    try:
        result = transcribe_file(file_path, backend=backend)
        print("\n=== TRANSCRIPTION RESULT ===")
        print(f"Raw text: {result['raw_text']}")
        print(f"\nNumber of segments: {len(result['segments'])}")

        print("\n=== SEGMENTS ===")
        for i, segment in enumerate(result['segments']):
            print(f"Segment {i+1}: [{segment['start']:.2f}s - {segment['end']:.2f}s] "
                  f"(confidence: {segment['confidence']:.2f})")
            print(f"  Text: {segment['text']}")

        # Save result to JSON file
        output_file = Path(file_path).with_suffix('.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\nResult saved to: {output_file}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
