#!/usr/bin/env python3
"""
Master Aggregator - Combine All Subjects
========================================

Creates a single master transcript and JSON file combining all processed subjects.

Usage:
    python master_aggregator.py --batch-dir output/batch_20250916_165800 --output-dir master_output
"""

import json
import argparse
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime
import re


class MasterAggregator:
    """Aggregates all subjects into master files."""
    
    def __init__(self, batch_dir: Path, output_dir: Path):
        self.batch_dir = Path(batch_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def discover_subjects(self) -> List[str]:
        """Discover all processed subjects in batch directory."""
        subjects = []
        for item in self.batch_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # Check if it has the required files
                json_files = list(item.glob("*.json"))
                txt_files = list(item.glob("*_MASTER_TRANSCRIPT.txt"))
                if json_files and txt_files:
                    subjects.append(item.name)
        return sorted(subjects)
    
    def load_subject_data(self, subject: str) -> Dict[str, Any]:
        """Load JSON and transcript data for a subject."""
        subject_dir = self.batch_dir / subject
        
        # Load JSON file
        json_files = list(subject_dir.glob("*.json"))
        json_data = {}
        if json_files:
            with open(json_files[0], 'r', encoding='utf-8') as f:
                json_data = json.load(f)
        
        # Load transcript file
        txt_files = list(subject_dir.glob("*_MASTER_TRANSCRIPT.txt"))
        transcript_text = ""
        if txt_files:
            with open(txt_files[0], 'r', encoding='utf-8') as f:
                transcript_text = f.read()
        
        return {
            "subject": subject,
            "json_data": json_data,
            "transcript": transcript_text,
            "json_file": str(json_files[0]) if json_files else None,
            "txt_file": str(txt_files[0]) if txt_files else None
        }
    
    def create_master_transcript(self, subjects_data: List[Dict[str, Any]]) -> str:
        """Create a single master transcript combining all subjects."""
        master_lines = []
        
        # Header
        master_lines.append("TRUTH WEAVER MASTER TRANSCRIPT")
        master_lines.append("=" * 80)
        master_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        master_lines.append(f"Total Subjects: {len(subjects_data)}")
        master_lines.append("")
        
        # Table of Contents
        master_lines.append("TABLE OF CONTENTS")
        master_lines.append("-" * 40)
        for i, data in enumerate(subjects_data, 1):
            subject = data["subject"]
            # Count sessions in transcript
            transcript = data["transcript"]
            session_count = len(re.findall(r'^SESSION \d+:', transcript, re.MULTILINE))
            master_lines.append(f"{i:2d}. {subject.upper()} ({session_count} sessions)")
        master_lines.append("")
        master_lines.append("=" * 80)
        master_lines.append("")
        
        # Individual subject transcripts
        for i, data in enumerate(subjects_data, 1):
            subject = data["subject"]
            transcript = data["transcript"]
            
            master_lines.append(f"SUBJECT {i}: {subject.upper()}")
            master_lines.append("=" * 80)
            master_lines.append("")
            
            if transcript:
                master_lines.append(transcript)
            else:
                master_lines.append("[NO TRANSCRIPT AVAILABLE]")
            
            master_lines.append("")
            master_lines.append("=" * 80)
            master_lines.append("")
        
        return "\n".join(master_lines)
    
    def create_master_json(self, subjects_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a single master JSON combining all subjects."""
        master_json = {
            "master_analysis": {
                "timestamp": datetime.now().isoformat(),
                "total_subjects": len(subjects_data),
                "batch_directory": str(self.batch_dir)
            },
            "subjects": {},
            "aggregate_statistics": {
                "total_deception_patterns": 0,
                "subjects_with_contradictions": 0,
                "deception_types": {},
                "programming_languages": {},
                "experience_claims": [],
                "skill_masteries": {},
                "leadership_patterns": {}
            }
        }
        
        # Process each subject
        for data in subjects_data:
            subject = data["subject"]
            json_data = data["json_data"]
            
            if json_data:
                master_json["subjects"][subject] = json_data
                
                # Update aggregate statistics
                deception_patterns = json_data.get("deception_patterns", [])
                master_json["aggregate_statistics"]["total_deception_patterns"] += len(deception_patterns)
                
                if deception_patterns:
                    master_json["aggregate_statistics"]["subjects_with_contradictions"] += 1
                
                # Count deception types
                for pattern in deception_patterns:
                    lie_type = pattern.get("lie_type", "unknown")
                    master_json["aggregate_statistics"]["deception_types"][lie_type] = \
                        master_json["aggregate_statistics"]["deception_types"].get(lie_type, 0) + 1
                
                # Analyze revealed truth
                revealed_truth = json_data.get("revealed_truth", {})
                
                # Programming languages
                prog_lang = revealed_truth.get("programming_language", "unknown")
                if prog_lang != "unknown":
                    master_json["aggregate_statistics"]["programming_languages"][prog_lang] = \
                        master_json["aggregate_statistics"]["programming_languages"].get(prog_lang, 0) + 1
                
                # Experience claims
                prog_exp = revealed_truth.get("programming_experience", "unknown")
                if prog_exp != "unknown":
                    master_json["aggregate_statistics"]["experience_claims"].append({
                        "subject": subject,
                        "experience": prog_exp
                    })
                
                # Skill mastery
                skill_mastery = revealed_truth.get("skill_mastery", "unknown")
                if skill_mastery != "unknown":
                    master_json["aggregate_statistics"]["skill_masteries"][skill_mastery] = \
                        master_json["aggregate_statistics"]["skill_masteries"].get(skill_mastery, 0) + 1
                
                # Leadership patterns
                leadership = revealed_truth.get("leadership_claims", "unknown")
                if leadership != "unknown":
                    master_json["aggregate_statistics"]["leadership_patterns"][leadership] = \
                        master_json["aggregate_statistics"]["leadership_patterns"].get(leadership, 0) + 1
        
        return master_json
    
    def generate_master_files(self) -> Dict[str, str]:
        """Generate master transcript and JSON files."""
        print("Discovering subjects...")
        subjects = self.discover_subjects()
        
        if not subjects:
            print("No subjects found in batch directory")
            return {}
        
        print(f"Found {len(subjects)} subjects: {subjects}")
        
        # Load all subject data
        print("Loading subject data...")
        subjects_data = []
        for subject in subjects:
            data = self.load_subject_data(subject)
            subjects_data.append(data)
            print(f"  Loaded: {subject}")
        
        # Create master transcript
        print("Creating master transcript...")
        master_transcript = self.create_master_transcript(subjects_data)
        
        # Create master JSON
        print("Creating master JSON...")
        master_json = self.create_master_json(subjects_data)
        
        # Save files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Master transcript file
        transcript_file = self.output_dir / f"MASTER_TRANSCRIPT_ALL_SUBJECTS_{timestamp}.txt"
        transcript_file.write_text(master_transcript, encoding="utf-8")
        print(f"Saved master transcript: {transcript_file}")
        
        # Master JSON file
        json_file = self.output_dir / f"MASTER_ANALYSIS_ALL_SUBJECTS_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(master_json, f, indent=2, ensure_ascii=False)
        print(f"Saved master JSON: {json_file}")
        
        # Summary report
        summary_file = self.output_dir / f"MASTER_SUMMARY_{timestamp}.txt"
        self.create_summary_report(summary_file, master_json, subjects_data)
        print(f"Saved summary report: {summary_file}")
        
        return {
            "transcript_file": str(transcript_file),
            "json_file": str(json_file),
            "summary_file": str(summary_file),
            "subjects_processed": len(subjects)
        }
    
    def create_summary_report(self, summary_file: Path, master_json: Dict[str, Any], subjects_data: List[Dict[str, Any]]):
        """Create a human-readable summary report."""
        stats = master_json["aggregate_statistics"]
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("TRUTH WEAVER MASTER ANALYSIS SUMMARY\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"Analysis Date: {master_json['master_analysis']['timestamp']}\n")
            f.write(f"Total Subjects Analyzed: {master_json['master_analysis']['total_subjects']}\n\n")
            
            f.write("DECEPTION ANALYSIS OVERVIEW:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Total Deception Patterns Found: {stats['total_deception_patterns']}\n")
            f.write(f"Subjects with Contradictions: {stats['subjects_with_contradictions']}\n")
            f.write(f"Deception Rate: {stats['subjects_with_contradictions']/len(subjects_data)*100:.1f}%\n\n")
            
            if stats['deception_types']:
                f.write("Deception Types Detected:\n")
                for lie_type, count in stats['deception_types'].items():
                    f.write(f"  - {lie_type}: {count} subjects\n")
                f.write("\n")
            
            f.write("PROGRAMMING PROFILE ANALYSIS:\n")
            f.write("-" * 40 + "\n")
            
            if stats['programming_languages']:
                f.write("Programming Languages:\n")
                for lang, count in sorted(stats['programming_languages'].items()):
                    f.write(f"  - {lang}: {count} subjects\n")
                f.write("\n")
            
            if stats['skill_masteries']:
                f.write("Skill Mastery Levels:\n")
                for level, count in sorted(stats['skill_masteries'].items()):
                    f.write(f"  - {level}: {count} subjects\n")
                f.write("\n")
            
            if stats['leadership_patterns']:
                f.write("Leadership Claim Patterns:\n")
                for pattern, count in sorted(stats['leadership_patterns'].items()):
                    f.write(f"  - {pattern}: {count} subjects\n")
                f.write("\n")
            
            f.write("INDIVIDUAL SUBJECT RESULTS:\n")
            f.write("-" * 40 + "\n")
            for data in subjects_data:
                subject = data["subject"]
                json_data = data["json_data"]
                if json_data:
                    deception_count = len(json_data.get("deception_patterns", []))
                    revealed_truth = json_data.get("revealed_truth", {})
                    experience = revealed_truth.get("programming_experience", "unknown")
                    language = revealed_truth.get("programming_language", "unknown")
                    
                    f.write(f"{subject}:\n")
                    f.write(f"  Experience: {experience}\n")
                    f.write(f"  Language: {language}\n")
                    f.write(f"  Contradictions: {deception_count}\n")
                    f.write("\n")


def main():
    parser = argparse.ArgumentParser(
        description="Master Aggregator - Combine all subjects into master files",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--batch-dir", required=True, help="Batch directory containing processed subjects")
    parser.add_argument("--output-dir", default="master_output", help="Output directory for master files")
    
    args = parser.parse_args()
    
    # Initialize aggregator
    aggregator = MasterAggregator(args.batch_dir, args.output_dir)
    
    try:
        results = aggregator.generate_master_files()
        
        if results:
            print(f"\n=== MASTER AGGREGATION COMPLETE ===")
            print(f"Subjects Processed: {results['subjects_processed']}")
            print(f"Master Transcript: {results['transcript_file']}")
            print(f"Master JSON: {results['json_file']}")
            print(f"Summary Report: {results['summary_file']}")
        else:
            print("No subjects found to aggregate")
            return 1
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
