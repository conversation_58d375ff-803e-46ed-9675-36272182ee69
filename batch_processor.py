#!/usr/bin/env python3
"""
Batch Truth Weaver Processor
============================

Efficiently process multiple subjects in parallel and generate comprehensive reports.

Usage:
    python batch_processor.py --audio-dir "audio copy/audio" --output-dir output --workers 4
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List, Optional
import re
import concurrent.futures
import time
from datetime import datetime

# Add src and scripts to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

try:
    from truth_weaver import TruthWeaver
except ImportError as e:
    print(f"Error importing TruthWeaver: {e}")
    sys.exit(1)


class BatchProcessor:
    """Batch processor for multiple Truth Weaver subjects."""
    
    def __init__(self, audio_dir: Path, output_dir: Path, backend: str = "assemblyai", max_workers: int = 4):
        self.audio_dir = Path(audio_dir)
        self.output_dir = Path(output_dir)
        self.backend = backend
        self.max_workers = max_workers
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create batch-specific output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.batch_dir = self.output_dir / f"batch_{timestamp}"
        self.batch_dir.mkdir(exist_ok=True)
        
    def discover_subjects(self) -> List[str]:
        """Discover all subjects in the audio directory."""
        audio_files = list(self.audio_dir.glob("*.mp3"))
        subjects = set()
        
        for audio_file in audio_files:
            # Extract subject name (everything before the last underscore and number)
            match = re.match(r'^(.+?)_\d+\.mp3$', audio_file.name)
            if match:
                subjects.add(match.group(1))
        
        return sorted(subjects)
    
    def process_subject_wrapper(self, subject: str) -> Dict[str, Any]:
        """Wrapper for processing a single subject with error handling."""
        try:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] Starting {subject}...")
            
            # Create subject-specific output directory
            subject_dir = self.batch_dir / subject
            subject_dir.mkdir(exist_ok=True)
            
            # Initialize Truth Weaver for this subject
            weaver = TruthWeaver(self.audio_dir, subject_dir, self.backend)
            
            # Process the subject
            result = weaver.process_subject(subject)
            
            if result:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ✓ Completed {subject}")
                return {
                    "subject": subject,
                    "status": "success",
                    "result": result,
                    "output_dir": str(subject_dir)
                }
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ✗ Failed {subject} - No result")
                return {
                    "subject": subject,
                    "status": "failed",
                    "error": "No result generated",
                    "output_dir": str(subject_dir)
                }
                
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✗ Error processing {subject}: {e}")
            return {
                "subject": subject,
                "status": "error",
                "error": str(e),
                "output_dir": str(subject_dir) if 'subject_dir' in locals() else None
            }
    
    def process_all(self) -> Dict[str, Any]:
        """Process all subjects in parallel."""
        subjects = self.discover_subjects()
        
        if not subjects:
            print("No subjects found in audio directory")
            return {"subjects": [], "results": {}}
        
        print(f"Found {len(subjects)} subjects: {subjects}")
        print(f"Processing with {self.max_workers} workers using {self.backend} backend")
        print(f"Output directory: {self.batch_dir}")
        
        start_time = time.time()
        results = {}
        
        # Process subjects in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all jobs
            future_to_subject = {
                executor.submit(self.process_subject_wrapper, subject): subject 
                for subject in subjects
            }
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_subject):
                subject = future_to_subject[future]
                try:
                    result = future.result()
                    results[subject] = result
                except Exception as e:
                    print(f"Exception processing {subject}: {e}")
                    results[subject] = {
                        "subject": subject,
                        "status": "exception",
                        "error": str(e)
                    }
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Generate batch summary
        summary = self.generate_batch_summary(results, processing_time)
        
        # Save batch results
        batch_results_file = self.batch_dir / "batch_results.json"
        with open(batch_results_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": summary,
                "results": results
            }, f, indent=2, ensure_ascii=False)
        
        # Save batch summary report
        summary_file = self.batch_dir / "batch_summary.txt"
        self.save_summary_report(summary_file, summary, results)
        
        print(f"\n=== BATCH PROCESSING COMPLETE ===")
        print(f"Total time: {processing_time:.1f} seconds")
        print(f"Results saved to: {self.batch_dir}")
        
        return {
            "summary": summary,
            "results": results,
            "batch_dir": str(self.batch_dir)
        }
    
    def generate_batch_summary(self, results: Dict[str, Any], processing_time: float) -> Dict[str, Any]:
        """Generate a summary of batch processing results."""
        total_subjects = len(results)
        successful = sum(1 for r in results.values() if r.get("status") == "success")
        failed = sum(1 for r in results.values() if r.get("status") in ["failed", "error", "exception"])
        
        # Analyze deception patterns across all subjects
        deception_types = {}
        total_contradictions = 0
        
        for subject, result in results.items():
            if result.get("status") == "success" and "result" in result:
                deception_patterns = result["result"].get("deception_patterns", [])
                total_contradictions += len(deception_patterns)
                
                for pattern in deception_patterns:
                    lie_type = pattern.get("lie_type", "unknown")
                    deception_types[lie_type] = deception_types.get(lie_type, 0) + 1
        
        return {
            "timestamp": datetime.now().isoformat(),
            "processing_time_seconds": round(processing_time, 2),
            "total_subjects": total_subjects,
            "successful": successful,
            "failed": failed,
            "success_rate": round(successful / total_subjects * 100, 1) if total_subjects > 0 else 0,
            "total_contradictions_found": total_contradictions,
            "deception_types": deception_types,
            "backend_used": self.backend
        }
    
    def save_summary_report(self, summary_file: Path, summary: Dict[str, Any], results: Dict[str, Any]):
        """Save a human-readable summary report."""
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("TRUTH WEAVER BATCH PROCESSING SUMMARY\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Timestamp: {summary['timestamp']}\n")
            f.write(f"Processing Time: {summary['processing_time_seconds']} seconds\n")
            f.write(f"Backend Used: {summary['backend_used']}\n\n")
            
            f.write("PROCESSING RESULTS:\n")
            f.write(f"  Total Subjects: {summary['total_subjects']}\n")
            f.write(f"  Successful: {summary['successful']}\n")
            f.write(f"  Failed: {summary['failed']}\n")
            f.write(f"  Success Rate: {summary['success_rate']}%\n\n")
            
            f.write("DECEPTION ANALYSIS:\n")
            f.write(f"  Total Contradictions Found: {summary['total_contradictions_found']}\n")
            if summary['deception_types']:
                f.write("  Deception Types:\n")
                for lie_type, count in summary['deception_types'].items():
                    f.write(f"    - {lie_type}: {count} subjects\n")
            f.write("\n")
            
            f.write("SUBJECT DETAILS:\n")
            for subject, result in sorted(results.items()):
                status = result.get("status", "unknown")
                f.write(f"  {subject}: {status.upper()}")
                
                if status == "success" and "result" in result:
                    deception_count = len(result["result"].get("deception_patterns", []))
                    f.write(f" ({deception_count} contradictions)")
                elif status in ["failed", "error", "exception"]:
                    error = result.get("error", "Unknown error")
                    f.write(f" - {error}")
                
                f.write("\n")


def main():
    parser = argparse.ArgumentParser(
        description="Batch Truth Weaver Processor",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--audio-dir", required=True, help="Directory containing audio files")
    parser.add_argument("--output-dir", default="output", help="Output directory")
    parser.add_argument("--backend", choices=["assemblyai", "stub"], default="assemblyai",
                       help="Transcription backend")
    parser.add_argument("--workers", type=int, default=4, help="Number of parallel workers")
    
    args = parser.parse_args()
    
    # Initialize batch processor
    processor = BatchProcessor(args.audio_dir, args.output_dir, args.backend, args.workers)
    
    try:
        results = processor.process_all()
        
        # Print final summary
        summary = results["summary"]
        print(f"\nFinal Summary:")
        print(f"  Processed: {summary['successful']}/{summary['total_subjects']} subjects")
        print(f"  Success Rate: {summary['success_rate']}%")
        print(f"  Total Contradictions: {summary['total_contradictions_found']}")
        
    except KeyboardInterrupt:
        print("\nBatch processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
