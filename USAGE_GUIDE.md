# Truth Weaver Usage Guide 🎯

## Complete System Overview

Your Truth Weaver system is now fully operational with the following components:

### 🔧 Core Components

1. **`truth_weaver.py`** - Main orchestrator for single subject processing
2. **`batch_processor.py`** - Parallel batch processing for multiple subjects  
3. **`validate_output.py`** - Output validation and quality checking
4. **`test_truth_weaver.py`** - Comprehensive test suite
5. **Enhanced ASR pipeline** - Improved audio transcription with speaker detection
6. **Advanced analysis engine** - Sophisticated contradiction detection

## 🚀 Getting Started

### Step 1: Test the System
```bash
# Run quick test with sample data
python test_truth_weaver.py --quick-test

# This will:
# - Create sample contradictory transcripts
# - Process them through the full pipeline
# - Validate outputs
# - Show sample results
```

### Step 2: Process Real Audio Files

#### Single Subject Processing
```bash
# Process one subject with AssemblyAI (recommended)
python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio" --output-dir output

# Process with stub backend (for testing without API calls)
python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio" --output-dir output --backend stub
```

#### Batch Processing (All Subjects)
```bash
# Process all subjects in parallel
python batch_processor.py --audio-dir "audio copy/audio" --output-dir output --workers 4

# This will:
# - Discover all subjects automatically
# - Process them in parallel (4 workers)
# - Generate comprehensive batch report
# - Create organized output structure
```

### Step 3: Validate Results
```bash
# Validate single files
python validate_output.py --json-file output/atlas_2025.json
python validate_output.py --txt-file output/atlas_2025_MASTER_TRANSCRIPT.txt

# Validate entire batch
python validate_output.py --batch-dir output/batch_20241216_143022
```

## 📊 Expected Outputs

### For Each Subject, You Get:

1. **`{subject}.json`** - Truth Weaver analysis in required format
2. **`{subject}_MASTER_TRANSCRIPT.txt`** - Complete transcript with sessions
3. **`{subject}_submission_scroll.txt`** - Human-readable analysis report

### Sample JSON Output Structure:
```json
{
  "shadow_id": "phoenix_2024",
  "revealed_truth": {
    "programming_experience": "3 years",
    "programming_language": "python", 
    "skill_mastery": "intermediate",
    "leadership_claims": "fabricated",
    "team_experience": "individual contributor",
    "skills and other keywords": ["Machine Learning"]
  },
  "deception_patterns": [
    {
      "lie_type": "experience_inflation",
      "contradictory_claims": ["6 years", "3 years", "2 months"]
    }
  ]
}
```

## 🎯 Key Features Implemented

### ✅ Audio Processing
- **AssemblyAI Integration**: High-quality transcription with speaker detection
- **Stub Backend**: Offline testing capability
- **Enhanced Segmentation**: Better sentence and speaker boundary detection
- **Confidence Scoring**: Audio quality and transcription reliability metrics

### ✅ Contradiction Detection
- **Experience Inflation**: Detects inconsistent years of experience claims
- **Leadership Fabrication**: Identifies contradictions between leadership claims and copying admissions
- **Skill Misrepresentation**: Analyzes technical skill consistency
- **Pattern Recognition**: Advanced keyword matching and context analysis

### ✅ Truth Extraction
- **Conservative Estimation**: Uses most reliable/conservative claims
- **Evidence Weighting**: Prioritizes high-confidence statements
- **Semantic Analysis**: Understands context and implications
- **Structured Output**: Matches exact required JSON format

### ✅ Quality Assurance
- **Format Validation**: Ensures outputs match competition requirements
- **Content Validation**: Checks for meaningful analysis results
- **Error Handling**: Graceful handling of transcription failures
- **Comprehensive Testing**: Full test suite with sample data

## 🏆 Competition Deliverables

Your system generates all required deliverables:

### 1. Transcript File (.txt) ✅
- Contains audio-to-text conversion output
- Clearly separated and labeled sessions
- Includes confidence scores and timestamps
- Evaluated for character similarity against ground truth

### 2. Final JSON File (.json) ✅
- Structured JSON in exact required format
- Contains `shadow_id`, `revealed_truth`, and `deception_patterns`
- Evaluated using Jaccard similarity
- Handles synonyms and semantic equivalence

### 3. Source Code Archive (.zip) ✅
- All Python source files included
- No model weights (only scripts)
- Clean, documented, and modular code
- Ready for submission

## 🔍 Advanced Usage

### Custom Analysis Parameters
```bash
# Adjust worker count for batch processing
python batch_processor.py --audio-dir "audio copy/audio" --workers 8

# Use different output directories
python truth_weaver.py --subject phoenix_2024 --output-dir custom_output
```

### Debugging and Development
```bash
# Create sample data for testing
python test_truth_weaver.py --create-sample-data

# Clean up test files
python test_truth_weaver.py --cleanup

# Run full test with real audio (if available)
python test_truth_weaver.py --full-test
```

## 📈 Performance Optimization

### For Best Results:
1. **Use AssemblyAI backend** for real audio processing
2. **Set appropriate worker count** (4-8 workers for batch processing)
3. **Validate outputs** before submission
4. **Test with sample data** first to verify system functionality

### Troubleshooting:
- If AssemblyAI fails, system falls back gracefully
- Stub backend available for offline testing
- Comprehensive error logging and reporting
- Validation tools help identify issues early

## 🎭 System Capabilities

Your Truth Weaver successfully:
- ✅ Processes multiple audio sessions per subject
- ✅ Detects experience inflation (6 years → 3 years → 2 months)
- ✅ Identifies leadership fabrication (claims vs. reality)
- ✅ Extracts conservative truth estimates
- ✅ Generates competition-compliant outputs
- ✅ Handles batch processing efficiently
- ✅ Validates output quality automatically

## 🚀 Ready for Competition!

Your Truth Weaver system is now complete and ready for the competition. It includes:

1. **Complete Pipeline**: Audio → Transcript → Analysis → JSON
2. **Quality Assurance**: Validation and testing tools
3. **Batch Processing**: Efficient handling of multiple subjects
4. **Error Handling**: Robust failure recovery
5. **Documentation**: Comprehensive guides and examples

Run the batch processor on your audio files to generate all competition deliverables! 🏆
