#!/usr/bin/env python3
"""
Selective Clean Formatter
========================

Removes punctuation and converts to lowercase ONLY in the transcript text,
while keeping the session tags (e.g., atlas_2025_1.mp4:) unchanged.

Usage:
    python selective_clean_formatter.py --input CLEAN_MASTER_TRANSCRIPT.txt --output SELECTIVE_CLEAN_TRANSCRIPT.txt
"""

import argparse
import re
from pathlib import Path


def selective_clean_transcript(input_file: Path, output_file: Path):
    """Clean transcript text while preserving session tags."""
    
    print(f"Reading transcript from: {input_file}")
    
    # Read the input file
    content = input_file.read_text(encoding="utf-8")
    
    # Split into lines
    lines = content.split('\n')
    
    cleaned_lines = []
    
    for line in lines:
        if line.strip():  # Skip empty lines
            # Check if line contains a session tag (filename.mp4:)
            match = re.match(r'^([^:]+\.mp4:)\s*(.*)', line)
            
            if match:
                # Extract session tag and text content
                session_tag = match.group(1)  # Keep as-is
                text_content = match.group(2)  # Clean this part
                
                # Clean the text content:
                # 1. Convert to lowercase
                text_lower = text_content.lower()
                
                # 2. Remove punctuation, keep only letters and spaces
                text_clean = re.sub(r'[^a-z\s]', '', text_lower)
                
                # 3. Clean up multiple spaces
                text_clean = re.sub(r'\s+', ' ', text_clean)
                
                # 4. Strip leading/trailing spaces
                text_clean = text_clean.strip()
                
                # Combine session tag with cleaned text
                if text_clean:
                    cleaned_line = f"{session_tag} {text_clean}"
                    cleaned_lines.append(cleaned_line)
            else:
                # If no session tag found, treat as regular line (shouldn't happen in this format)
                cleaned_lines.append(line)
        else:
            # Preserve empty lines for formatting
            cleaned_lines.append('')
    
    # Join lines back together
    cleaned_content = '\n'.join(cleaned_lines)
    
    # Write to output file
    output_file.write_text(cleaned_content, encoding="utf-8")
    
    print(f"Selectively cleaned transcript saved to: {output_file}")
    print(f"Original lines: {len(lines)}")
    print(f"Cleaned lines: {len(cleaned_lines)}")


def main():
    parser = argparse.ArgumentParser(
        description="Selectively clean transcript - remove punctuation and lowercase text while preserving session tags",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--input", default="CLEAN_MASTER_TRANSCRIPT.txt",
                       help="Input transcript file")
    parser.add_argument("--output", default="SELECTIVE_CLEAN_TRANSCRIPT.txt",
                       help="Output selectively cleaned transcript file")
    
    args = parser.parse_args()
    
    input_file = Path(args.input)
    if not input_file.exists():
        print(f"Input file does not exist: {input_file}")
        return 1
    
    output_file = Path(args.output)
    
    try:
        selective_clean_transcript(input_file, output_file)
        print("Selective transcript cleaning completed successfully!")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
