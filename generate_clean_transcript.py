#!/usr/bin/env python3
"""
Generate Clean Master Transcript
===============================

Creates a simple, clean master transcript in the format:
filename.mp4: transcribed text

Usage:
    python generate_clean_transcript.py --batch-dir output/batch_20250916_165800 --output clean_transcript.txt
"""

import argparse
import re
from pathlib import Path
from typing import List


def extract_clean_transcript(batch_dir: Path, output_file: Path):
    """Extract clean transcript from all subjects."""
    
    # Discover subjects
    subjects = []
    for item in batch_dir.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            txt_files = list(item.glob("*_MASTER_TRANSCRIPT.txt"))
            if txt_files:
                subjects.append((item.name, txt_files[0]))
    
    subjects.sort(key=lambda x: x[0])  # Sort by subject name
    
    clean_lines = []
    
    for subject, transcript_file in subjects:
        print(f"Processing {subject}...")
        
        transcript = transcript_file.read_text(encoding="utf-8")
        
        # Extract sessions from transcript
        sessions = re.split(r'^SESSION \d+:', transcript, flags=re.MULTILINE)
        session_headers = re.findall(r'^SESSION \d+:[^\n]*', transcript, flags=re.MULTILINE)
        
        if len(sessions) > 1 and session_headers:
            # Process each session
            for i, (header, session_content) in enumerate(zip(session_headers, sessions[1:]), 1):
                # Extract just the full text from each session
                full_text_match = re.search(r'FULL TEXT:\s*(.*?)\n\s*(?:Overall Confidence:|TIMESTAMPED BREAKDOWN:|$)', session_content, flags=re.S|re.I)
                if full_text_match:
                    full_text = full_text_match.group(1).strip()
                    if full_text:
                        # Format as requested: filename.mp4: transcribed text
                        clean_lines.append(f"{subject}_{i}.mp4: {full_text}")
                        clean_lines.append("")
        else:
            # Fallback: treat entire content as one session
            full_text_match = re.search(r'FULL TEXT:\s*(.*?)\n\s*(?:Overall Confidence:|$)', transcript, flags=re.S|re.I)
            if full_text_match:
                full_text = full_text_match.group(1).strip()
                if full_text:
                    clean_lines.append(f"{subject}_1.mp4: {full_text}")
                    clean_lines.append("")
    
    # Write clean transcript
    output_file.write_text("\n".join(clean_lines), encoding="utf-8")
    print(f"Clean transcript saved to: {output_file}")
    print(f"Total lines: {len([line for line in clean_lines if line.strip()])}")


def main():
    parser = argparse.ArgumentParser(
        description="Generate clean master transcript",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--batch-dir", required=True, 
                       help="Batch directory containing processed subjects")
    parser.add_argument("--output", default="clean_master_transcript.txt",
                       help="Output file for clean transcript")
    
    args = parser.parse_args()
    
    batch_dir = Path(args.batch_dir)
    if not batch_dir.exists():
        print(f"Batch directory does not exist: {batch_dir}")
        return 1
    
    output_file = Path(args.output)
    
    try:
        extract_clean_transcript(batch_dir, output_file)
        print("Clean transcript generation completed successfully!")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
