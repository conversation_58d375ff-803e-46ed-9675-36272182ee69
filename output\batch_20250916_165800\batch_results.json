{"summary": {"timestamp": "2025-09-16T16:59:12.419235", "processing_time_seconds": 71.66, "total_subjects": 8, "successful": 8, "failed": 0, "success_rate": 100.0, "total_contradictions_found": 1, "deception_types": {"experience_inflation": 1}, "backend_used": "assemblyai"}, "results": {"crius_2025": {"subject": "crius_2025", "status": "success", "result": {"shadow_id": "crius_2025", "revealed_truth": {"programming_experience": "unknown", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "claimed", "team_experience": "claimed team lead", "skills and other keywords": []}, "deception_patterns": []}, "output_dir": "output\\batch_20250916_165800\\crius_2025"}, "hyperion_2022": {"subject": "hyperion_2022", "status": "success", "result": {"shadow_id": "hyperion_2022", "revealed_truth": {"programming_experience": "unknown", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "claimed", "team_experience": "claimed team lead", "skills and other keywords": ["Model"]}, "deception_patterns": []}, "output_dir": "output\\batch_20250916_165800\\hyperion_2022"}, "atlas_2025": {"subject": "atlas_2025", "status": "success", "result": {"shadow_id": "atlas_2025", "revealed_truth": {"programming_experience": "unknown", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "none", "team_experience": "individual contributor", "skills and other keywords": []}, "deception_patterns": []}, "output_dir": "output\\batch_20250916_165800\\atlas_2025"}, "eos_2023": {"subject": "eos_2023", "status": "success", "result": {"shadow_id": "eos_2023", "revealed_truth": {"programming_experience": "8 years", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "claimed", "team_experience": "claimed team lead", "skills and other keywords": []}, "deception_patterns": []}, "output_dir": "output\\batch_20250916_165800\\eos_2023"}, "oceanus_2022": {"subject": "oceanus_2022", "status": "success", "result": {"shadow_id": "oceanus_2022", "revealed_truth": {"programming_experience": "unknown", "programming_language": "c", "skill_mastery": "intermediate", "leadership_claims": "none", "team_experience": "individual contributor", "skills and other keywords": []}, "deception_patterns": []}, "output_dir": "output\\batch_20250916_165800\\oceanus_2022"}, "selene_2024": {"subject": "selene_2024", "status": "success", "result": {"shadow_id": "selene_2024", "revealed_truth": {"programming_experience": "unknown", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "none", "team_experience": "individual contributor", "skills and other keywords": ["Machine Learning", "Model"]}, "deception_patterns": []}, "output_dir": "output\\batch_20250916_165800\\selene_2024"}, "titan_2023": {"subject": "titan_2023", "status": "success", "result": {"shadow_id": "titan_2023", "revealed_truth": {"programming_experience": "2 years", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "claimed", "team_experience": "claimed team lead", "skills and other keywords": []}, "deception_patterns": [{"lie_type": "experience_inflation", "contradictory_claims": ["7 years", "2 years"]}]}, "output_dir": "output\\batch_20250916_165800\\titan_2023"}, "rhea_2024": {"subject": "rhea_2024", "status": "success", "result": {"shadow_id": "rhea_2024", "revealed_truth": {"programming_experience": "6 years", "programming_language": "java", "skill_mastery": "intermediate", "leadership_claims": "claimed", "team_experience": "claimed team lead", "skills and other keywords": []}, "deception_patterns": []}, "output_dir": "output\\batch_20250916_165800\\rhea_2024"}}}