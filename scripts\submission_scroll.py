#!/usr/bin/env python3
import re
import json
from pathlib import Path
from collections import Counter, defaultdict
import argparse
import statistics
from typing import Dict, Any, List, Tuple

# --- constants / keywords ---
NUM_WORDS = {
    'zero': 0, 'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
    'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10
}

LEAD_KEYWORDS = [
    r"\blead\b", r"\bled\b", r"\bteam lead\b", r"\bheaded\b", r"\bmanaged\b",
    r"\bdrove the architecture\b", r"\bhandled the full life cycle\b", r"\bhandled it\b", r"\bownership\b",
    r"\bbuilt incredible systems\b", r"\barchitected\b", r"\bdesigned from scratch\b", r"\bfrom the ground up\b",
    r"\bmy team\b", r"\bteam of \d+\b", r"\bI managed\b", r"\bunder my leadership\b", r"\bI directed\b"
]
COPY_KEYWORDS = [
    r"\bcopy(?:ied|ing)?\b", r"\breuse(?:d|ing)?\b", r"\bassembled\b", r"\bstitch(?:ed|ing)?\b",
    r"\bstitc?hing\b", r"\bduct tape\b", r"\bquick fixes\b", r"\bfollowed tutorials\b",
    r"\bstack overflow\b", r"\bcopied from\b", r"\bfound online\b", r"\bmodified existing\b",
    r"\bwork alone\b", r"\bnever been comfortable with.*people\b", r"\bjust.*debugging\b"
]
LANG_KEYWORDS = ["python", "java", r"c\+\+", "javascript", "sql", "r", "scala", "go", r"c#", r"\bc\b"]
SKILL_KEYWORDS = ["machine learning", "deep learning", "nlp", "computer vision", "data science", "deployment", "model"]


# --- helper utilities ---
def _short_snippet(text: str, max_len: int = 160) -> str:
    t = text.strip()
    if len(t) <= max_len:
        return t
    # prefer cutting at sentence boundary if possible
    m = re.search(r"(.{0,%d}\.)" % (max_len - 1), t)
    if m:
        return m.group(1).strip()
    return t[:max_len].rstrip() + "..."


def _unique_by_session(evidence: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    seen = set()
    out = []
    for e in evidence:
        sid = e.get("session_id")
        if sid in seen:
            continue
        seen.add(sid)
        out.append(e)
    return out


def parse_master_transcript(path: Path) -> Dict[str, str]:
    """
    Parse a MASTER_TRANSCRIPT.txt into session blocks.
    Tries to find blocks like:
        SESSION 1: Title
        ...text...
    If that pattern isn't found, splits by blank-line-separated 'Session' headings or by 'SESSION ' pattern.
    """
    text = path.read_text(encoding="utf8")
    sessions: Dict[str, str] = {}

    # Try pattern: lines like "SESSION 1: Title" or "Session 1 - Title"
    pattern = re.compile(r"(?mi)^(?:SESSION|Session)\s*\d+\s*[:\-]\s*(.+?)\s*$\n(.*?)(?=(?:\n(?:SESSION|Session)\s*\d+\s*[:\-])|\Z)", flags=re.S | re.M)
    matches = pattern.findall(text)
    if matches:
        for title, block in matches:
            title_clean = re.sub(r"\s+", " ", title).strip()
            sessions[title_clean] = block.strip()
        return sessions

    # Fallback: split by "SESSION" tokens
    parts = re.split(r"(?mi)^(?:SESSION|Session)\b.*$", text, flags=re.M)
    headers = re.findall(r"(?mi)^(?:SESSION|Session)\b.*$", text, flags=re.M)
    if len(parts) > 1 and headers:
        for i, (hdr, p) in enumerate(zip(headers, parts[1:]), start=1):
            title = hdr.strip()
            sessions[title or f"session_{i}"] = p.strip()
        return sessions

    # Very last fallback: split by two or more newlines and label generically
    parts = [p.strip() for p in re.split(r"\n\s*\n+", text) if p.strip()]
    if len(parts) == 1:
        # Single large transcript, return as one session
        sessions["full_transcript"] = parts[0]
    else:
        for i, p in enumerate(parts, start=1):
            sessions[f"session_{i}"] = p
    return sessions


def extract_numeric_years(text: str) -> List[int]:
    """
    Extract numeric year-of-experience mentions from text.
    Supports both digits (e.g., '5', '10 years') and small number words (e.g., 'five years').
    Returns list of integer values found (may contain duplicates).
    """
    found: List[int] = []
    t = text.lower()

    # Find explicit numbers like "5 years", "10 yrs", "6 years of experience"
    patterns = [
        r"\b(\d{1,2})\s*(?:\+?\s*)?(?:years?|yrs?)\s*(?:of\s+)?(?:experience|programming|coding|development)?\b",
        r"\b(?:for|over|about|around|nearly)\s+(\d{1,2})\s*(?:years?|yrs?)\b",
        r"\b(\d{1,2})\s*(?:\+?\s*)?(?:years?|yrs?)\s+(?:in|of|with|doing)\b"
    ]

    for pattern in patterns:
        for m in re.findall(pattern, t):
            try:
                val = int(m)
                if 0 <= val <= 50:  # Reasonable range for experience
                    found.append(val)
            except Exception:
                continue

    # Find number words followed by 'year(s)'
    for word, val in NUM_WORDS.items():
        if re.search(rf"\b{word}\b\s+(?:years?|yrs?)\b", t):
            found.append(val)

    # Also capture patterns like "X-Y years" (take both ends)
    for m in re.findall(r"\b(\d{1,2})\s*-\s*(\d{1,2})\s*(?:years?|yrs?)\b", t):
        try:
            a, b = int(m[0]), int(m[1])
            if 0 <= a <= 50 and 0 <= b <= 50:
                found.extend([a, b])
        except Exception:
            continue

    return found


def find_any_keyword(text: str, patterns: List[str]) -> bool:
    t = text.lower()
    for p in patterns:
        if re.search(p, t, flags=re.I):
            return True
    return False


def extract_langs(text: str) -> List[str]:
    found = []
    t = text.lower()
    for k in LANG_KEYWORDS:
        if re.search(rf"\b{k}\b", t, flags=re.I):
            # normalize certain tokens
            token = k.replace(r"\+", "+").strip()
            token = token.replace(r"\b", "")
            token = token.strip()
            found.append(token)
    # dedupe while preserving order
    seen = set()
    out = []
    for f in found:
        if f not in seen:
            seen.add(f)
            out.append(f)
    return out


def extract_skills(text: str) -> List[str]:
    found = []
    t = text.lower()
    for k in SKILL_KEYWORDS:
        if k in t:
            found.append(k)
    # dedupe
    return sorted(set(found))


def extract_session_claims(session_block: str) -> Dict[str, Any]:
    m = re.search(r"FULL TEXT:\n(.*?)\n\nTIMESTAMPED BREAKDOWN:", session_block, flags=re.S | re.I)
    if m:
        full_text = m.group(1).strip()
    else:
        full_text = session_block.strip()

    avg_conf = None
    m2 = re.search(r"Overall Confidence:\s*([\d\.]+)%", session_block, flags=re.I)
    if m2:
        try:
            avg_conf = float(m2.group(1)) / 100.0
        except Exception:
            avg_conf = None

    years = extract_numeric_years(full_text)
    leadership_claim = find_any_keyword(full_text, LEAD_KEYWORDS)
    copying_admission = find_any_keyword(full_text, COPY_KEYWORDS)
    langs = extract_langs(full_text)
    skills = extract_skills(full_text)

    evidence_snippet = ""
    # split into sentences or newline boundaries
    for sent in re.split(r"[.\n]", full_text):
        s = sent.strip()
        if not s:
            continue
        if leadership_claim and re.search(r"|".join(LEAD_KEYWORDS), s, flags=re.I):
            evidence_snippet = s
            break
        if copying_admission and re.search(r"|".join(COPY_KEYWORDS), s, flags=re.I):
            evidence_snippet = s
            break

    if not evidence_snippet:
        evidence_snippet = full_text.split(".")[0].strip()

    return {
        "text": full_text,
        "years": years,
        "leadership_claim": leadership_claim,
        "copying_admission": copying_admission,
        "languages": langs,
        "skills": skills,
        "avg_confidence": avg_conf or 0.0,
        "evidence_snippet": evidence_snippet
    }


def detect_contradictions(session_claims: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
    contradictions: List[Dict[str, Any]] = []

    # --- Experience inflation/contradiction ---
    year_entries: List[Dict[str, Any]] = []
    for sid, payload in session_claims.items():
        text = (payload.get("text") or "").lower()
        snippet = payload.get("evidence_snippet", "") or ""
        conf = float(payload.get("avg_confidence", 0.0)) if payload.get("avg_confidence") is not None else 0.0
        for y in payload.get("years", []):
            try:
                val = int(y)
            except Exception:
                continue
            note = None
            if "intern" in text or "internship" in text:
                note = "includes internships"
            if any(k in text for k in ("freelance", "consult", "consults", "contract")):
                note = (note + "; includes freelance/consulting") if note else "includes freelance/consulting"
            year_entries.append({
                "value": val,
                "session_id": sid,
                "snippet": _short_snippet(snippet or f"Claimed {val} years experience"),
                "note": note,
                "confidence": conf
            })

    # Keep highest-confidence year claim per session (if multiple)
    year_by_session: Dict[str, Dict[str, Any]] = {}
    for e in year_entries:
        sid = e["session_id"]
        if sid not in year_by_session or e["confidence"] > year_by_session[sid].get("confidence", 0.0):
            year_by_session[sid] = e

    year_values = sorted({v["value"] for v in year_by_session.values()}) if year_by_session else []

    if len(set(year_values)) > 1:
        # Create contradictory claims in the expected format
        contradictory_claims = []
        for sid in sorted(year_by_session.keys()):
            e = year_by_session[sid]
            claim_text = f"{e['value']} years"
            if e.get('note'):
                claim_text += f" ({e['note']})"
            contradictory_claims.append(claim_text)

        contradictions.append({
            "lie_type": "experience_inflation",
            "contradictory_claims": contradictory_claims
        })

    # --- Leadership vs copying contradiction ---
    leader_evidence = []
    copy_evidence = []
    for sid, payload in session_claims.items():
        text = (payload.get("text") or "")
        snippet = payload.get("evidence_snippet", "") or text[:160]
        conf = float(payload.get("avg_confidence", 0.0)) if payload.get("avg_confidence") is not None else 0.0
        if payload.get("leadership_claim"):
            leader_evidence.append({
                "session_id": sid,
                "snippet": _short_snippet(snippet),
                "confidence": conf
            })
        if payload.get("copying_admission"):
            copy_evidence.append({
                "session_id": sid,
                "snippet": _short_snippet(snippet),
                "confidence": conf
            })

    leader_evidence = _unique_by_session(leader_evidence)
    copy_evidence = _unique_by_session(copy_evidence)

    if leader_evidence and copy_evidence:
        contradictory_claims = [
            "Claimed leadership and team management",
            "Admitted working alone and copying others' work"
        ]
        contradictions.append({
            "lie_type": "leadership_fabrication",
            "contradictory_claims": contradictory_claims
        })

    return contradictions


# --- Enhanced infer_revealed_truth ---
def infer_revealed_truth(session_claims: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Programming experience: find the most conservative/reliable estimate
    all_years: List[int] = []
    qualifiers = set()
    for sid, payload in session_claims.items():
        yrs = payload.get("years", []) or []
        if yrs:
            for y in yrs:
                try:
                    val = int(y)
                    all_years.append(val)
                except Exception:
                    continue
            txt = (payload.get("text") or "").lower()
            if "intern" in txt or "internship" in txt:
                qualifiers.add("includes internships")
            if "freelance" in txt or "consult" in txt or "consults" in txt or "contract" in txt:
                qualifiers.add("includes freelance/consulting")

    prog_exp = "unknown"
    if all_years:
        vals = sorted(all_years)
        # Use the most conservative estimate (often the lower values are more truthful)
        if len(vals) == 1:
            prog_exp = f"{vals[0]} years"
        else:
            # Take the lower end of the range as more likely truthful
            conservative_estimate = vals[0] if len(vals) > 1 else vals[0]
            prog_exp = f"{conservative_estimate} years"

    # Programming language: majority vote (clean up tokens)
    all_langs = []
    for v in session_claims.values():
        langs = v.get("languages") or []
        all_langs.extend([str(l).lower() for l in langs if l])

    prog_lang = Counter(all_langs).most_common(1)[0][0] if all_langs else "unknown"

    # Skill mastery: conservative assessment based on contradictions
    combined_text = " ".join([v.get("text", "") for v in session_claims.values()]).lower()
    any_lead = any(v.get("leadership_claim") for v in session_claims.values())
    any_copy = any(v.get("copying_admission") for v in session_claims.values())

    if any_copy or "debugging" in combined_text or "learning" in combined_text:
        mastery = "intermediate"
    elif re.search(r"\bexpert\b|\bsenior\b|\badvanced\b", combined_text) and not any_copy:
        mastery = "advanced"
    else:
        mastery = "intermediate"

    # Leadership claims: be specific about fabrication
    leadership_claims = "none"
    team_experience = "individual contributor"

    if any_lead and any_copy:
        leadership_claims = "fabricated"
        team_experience = "individual contributor"
    elif any_lead and not any_copy:
        leadership_claims = "claimed"
        team_experience = "claimed team lead"
    elif any_copy:
        leadership_claims = "none"
        team_experience = "individual contributor"

    # Skills set: extract all mentioned skills
    skills_set = set()
    for v in session_claims.values():
        for s in (v.get("skills") or []):
            if s:
                skills_set.add(s.title())  # Capitalize for consistency

    revealed = {
        "programming_experience": prog_exp,
        "programming_language": prog_lang,
        "skill_mastery": mastery,
        "leadership_claims": leadership_claims,
        "team_experience": team_experience,
        "skills and other keywords": sorted(skills_set) if skills_set else []
    }
    return revealed


def build_submission_scroll(master_path: Path, subject: str, out_dir: Path) -> Dict[str, Any]:
    sessions = parse_master_transcript(master_path)
    session_claims: Dict[str, Dict[str, Any]] = {}
    for title, block in sessions.items():
        sid = title.replace(" ", "_").lower()
        session_claims[sid] = extract_session_claims(block)

    contradictions = detect_contradictions(session_claims)
    revealed = infer_revealed_truth(session_claims)
    final = {
        "shadow_id": subject,
        "revealed_truth": revealed,
        "deception_patterns": contradictions
    }

    out_dir.mkdir(parents=True, exist_ok=True)
    json_path = out_dir / f"{subject}.json"
    txt_path = out_dir / f"{subject}_submission_scroll.txt"
    json_path.write_text(json.dumps(final, indent=2), encoding="utf8")
    with open(txt_path, "w", encoding="utf8") as fh:
        fh.write("SUBMISSION SCROLL\n")
        fh.write("=================\n\n")
        fh.write(f"Subject: {subject}\n\n")
        fh.write("REVEALED TRUTH:\n")
        for k, v in revealed.items():
            fh.write(f"  {k}: {v}\n")
        fh.write("\nDECEPTION PATTERNS:\n")
        if contradictions:
            for c in contradictions:
                fh.write(f"- lie_type: {c.get('lie_type')}\n")
                fh.write(f"  contradictory_claims: {c.get('contradictory_claims')}\n")
                if 'evidence' in c:
                    fh.write("  evidence:\n")
                    for e in c['evidence']:
                        fh.write(f"    - session_id: {e.get('session_id')}, snippet: \"{e.get('snippet')}\", conf: {e.get('confidence')}\n")
                if 'summary' in c:
                    fh.write(f"  summary: {c.get('summary')}\n")
                fh.write("\n")
        else:
            fh.write("  None detected.\n")

    print("Wrote submission scroll JSON:", json_path)
    print("Wrote human-readable submission scroll:", txt_path)
    return final


def main():
    parser = argparse.ArgumentParser(prog="build_submission_scroll", description="Analyze MASTER_TRANSCRIPT and build submission scroll JSON + TXT")
    parser.add_argument("--master", required=True, help="Path to MASTER_TRANSCRIPT.txt")
    parser.add_argument("--subject", required=True, help="Subject / shadow_id (e.g. subject_01)")
    parser.add_argument("--out", default="out", help="Output folder")
    args = parser.parse_args()

    master_path = Path(args.master)
    if not master_path.exists():
        raise SystemExit(f"Master transcript not found: {master_path}")

    subject = args.subject
    out_dir = Path(args.out)
    result = build_submission_scroll(master_path, subject, out_dir)
    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    main()
