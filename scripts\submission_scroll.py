#!/usr/bin/env python3
"""
build_submission_scroll_aggressive.py

Aggressive contradiction detector for MASTER_TRANSCRIPT.

- Aggressive heuristics: hedges, mild corrections, paraphrases, and any differing numeric years flagged.
- Writes per-subject JSON and human-readable `_submission_scroll.txt` to the output folder.
"""
import re
import json
from pathlib import Path
from collections import Counter

# ---------------------------
# Aggressive pattern sets
# ---------------------------
NUM_WORDS = {'zero':0,'one':1,'two':2,'three':3,'four':4,'five':5,'six':6,'seven':7,'eight':8,'nine':9,'ten':10}

ASSERTIVE_VERBS = [
    r"\bi built\b", r"\bi wrote\b", r"\bi designed\b", r"\bi architected\b",
    r"\bi created\b", r"\bi led\b", r"\bi was the lead\b", r"\bi managed\b",
    r"\bi directed\b", r"\bi implemented\b", r"\bi ran the team\b",
    r"\bi built the backend\b", r"\bi built my backend\b", r"\bi built our\b",
    r"\bi was the front end department\b", r"\bI was the front end\b"
]

RETRACTION_PATTERNS = [
    r"\bshould correct\b", r"\btoo strong\b", r"\bnot the architect\b", r"\bwas not the architect\b",
    r"\bjust watched\b", r"\bjust used\b", r"\bran some scripts\b", r"\bI just want to be one\b",
    r"\bwas an internship\b", r"\binternship\b", r"\bI was an intern\b", r"\bI'm a junior\b",
    r"\bI'?m not an\b", r"\bI'?m not a\b", r"\bhe designed\b", r"\bhe provided\b",
    r"\bhe just provided\b", r"\bI the lead dev actually wrote\b", r"\barchitected is too strong\b",
    r"\bshould correct that wording\b"
]

LEAD_KEYWORDS = [r"\blead\b", r"\bled\b", r"\bteam lead\b", r"\bheaded\b", r"\bhead\b", r"\barchitect\b", r"\bprincipal\b", r"\bsenior\b", r"\bvisionary\b", r"\blead architect\b"]
COPY_KEYWORDS = [r"\bjust used\b", r"\bjust watched\b", r"\bran some scripts\b", r"\brun some scripts\b", r"\bcopy(?:ied|ing)?\b", r"\bcopied from\b", r"\bstack overflow\b"]
NEGATIVE_ADMISSION = [r"\binternship\b", r"\bI was an intern\b", r"\bI was a junior\b", r"\bI'?m not an\b", r"\bnot an architect\b", r"\bI just want to be one\b", r"\bnot a DevOps engineer\b"]
HEDGING = [r"\btoo strong\b", r"\bshould correct\b", r"\bI only\b", r"\bI typically\b", r"\bmostly\b", r"\bmostly just\b", r"\bmostly just watched\b"]

LANG_KEYWORDS = ["python","java","c++","javascript","sql","r","scala","go","c#","c"]
SKILL_KEYWORDS = ["machine learning","deep learning","nlp","computer vision","data science","deployment","model"]

# ---------------------------
# Parse master transcript into subjects -> sessions
# ---------------------------
def parse_master(path: Path):
    raw = path.read_text(encoding="utf8")
    text = raw.replace("\r\n","\n").replace("\r","\n")
    subj_pat = re.compile(r"(?mi)^SUBJECT\s*\d+\s*:\s*(.+)$", flags=re.M)
    headers = list(subj_pat.finditer(text))
    subjects = {}
    if headers:
        for i,h in enumerate(headers):
            name = h.group(1).strip()
            start = h.end()
            end = headers[i+1].start() if i+1 < len(headers) else len(text)
            block = text[start:end].strip()
            subjects[name] = _parse_sessions_from_subject_block(block)
    else:
        subjects["UNKNOWN"]=_parse_sessions_from_subject_block(text)
    return subjects

def _parse_sessions_from_subject_block(block_text: str):
    sessions = {}
    session_hdr_pattern = re.compile(r"(?mi)^(SESSION|Session)\s*\d+\s*[:\-]\s*(.*?)\s*$", flags=re.M)
    headers = list(session_hdr_pattern.finditer(block_text))
    if headers:
        for i,h in enumerate(headers):
            title = h.group(0).strip()
            start = h.end()
            end = headers[i+1].start() if i+1 < len(headers) else len(block_text)
            body = block_text[start:end].strip()
            sessions[title] = body
        return sessions
    # fallback: FULL TEXT segments
    parts = re.split(r"(?mi)(FULL TEXT:)", block_text)
    if len(parts) > 1:
        idx=0
        while idx < len(parts):
            if parts[idx].strip().upper()=="FULL TEXT:":
                content = parts[idx+1]
                sessions[f"session_{idx}"] = content.strip()
                idx += 2
            else:
                idx += 1
        if sessions: return sessions
    # final fallback: paragraph chunks
    chunks = [p.strip() for p in re.split(r"\n\s*\n+", block_text) if p.strip()]
    for i,c in enumerate(chunks,1):
        sessions[f"session_{i}"] = c
    return sessions

# ---------------------------
# Helpers: sentences, matching, numeric extraction
# ---------------------------
def find_sentences(text):
    sents = [s.strip() for s in re.split(r'(?<=[\.\?\!])\s+|\n', text) if s.strip()]
    return sents

def match_any(patterns, text):
    for p in patterns:
        if re.search(p, text, flags=re.I):
            return True
    return False

def extract_numeric_years(text):
    found=[]
    t=text.lower()
    for pat in [r"\b(\d{1,2})\s*(?:\+)?\s*(?:years?|yrs?)\b", r"\b(\d{1,2})\s*-\s*(\d{1,2})\s*(?:years?)\b"]:
        for m in re.findall(pat,t):
            if isinstance(m,tuple):
                for mm in m:
                    try: found.append(int(mm))
                    except: pass
            else:
                try: found.append(int(m))
                except: pass
    for w,v in NUM_WORDS.items():
        if re.search(rf"\b{re.escape(w)}\b\s+(?:years?|year)\b", t):
            found.append(v)
    if re.search(r"\bpast\s+year\b|\ba\s+year\b|\blast\s+year\b", t):
        found.append(1)
    if re.search(r"\bdecade\b", t):
        found.append(10)
    if re.search(r"\bfor the past year\b", t):
        found.append(1)
    return found

# ---------------------------
# Extract session-level claims
# ---------------------------
def extract_session_claims(block):
    m = re.search(r"FULL TEXT:\s*(.*?)\n\s*(?:Overall Confidence:|$)", block, flags=re.S|re.I)
    full = m.group(1).strip() if m else block.strip()
    conf = 0.0
    m2 = re.search(r"Overall Confidence:\s*([\d\.]+)%", block, flags=re.I)
    if m2:
        try: conf = float(m2.group(1))/100.0
        except: conf = 0.0
    sents = find_sentences(full)
    assertions = [s for s in sents if match_any(ASSERTIVE_VERBS,s)]
    retractions = [s for s in sents if match_any(RETRACTION_PATTERNS,s)]
    hedges = [s for s in sents if match_any(HEDGING,s)]
    lead = match_any(LEAD_KEYWORDS, full)
    copy = match_any(COPY_KEYWORDS, full)
    neg = match_any(NEGATIVE_ADMISSION, full)
    years = extract_numeric_years(full)
    langs = [k for k in LANG_KEYWORDS if re.search(rf"\b{k}\b", full, flags=re.I)]
    skills = [k for k in SKILL_KEYWORDS if k in full.lower()]
    snippet = sents[0] if sents else full[:160]
    for s in sents:
        if assertions and any(re.search(p,s,flags=re.I) for p in ASSERTIVE_VERBS):
            snippet = s; break
    if snippet=="" and retractions:
        snippet = retractions[0]
    return {"text":full, "assertions":assertions, "retractions":retractions, "hedges":hedges, "lead":lead, "copy":copy, "neg":neg, "years":years, "languages":langs, "skills":skills, "confidence":conf, "snippet":snippet}

# ---------------------------
# Aggressive contradiction detection and inference
# ---------------------------
def detect_aggressive_contradictions(session_claims):
    contradictions=[]
    # experience inflation (aggressive): any differing numeric years
    year_entries=[]
    for sid,p in session_claims.items():
        for y in p.get("years",[]):
            try: year_entries.append({"session_id":sid,"value":int(y),"snippet":p.get("snippet"),"confidence":p.get("confidence")})
            except: pass
    if year_entries:
        vals=set(e["value"] for e in year_entries)
        if len(vals) > 1:
            contradictions.append({"lie_type":"experience_inflation_aggressive","contradictory_claims":[f"{e['session_id']}: {e['value']} years" for e in year_entries],"evidence":year_entries})
    # leadership vs any downplay/hedge/copy -> aggressive flag
    leader=[sid for sid,p in session_claims.items() if p.get("lead")]
    downplay=[sid for sid,p in session_claims.items() if p.get("neg") or p.get("copy") or p.get("hedges") or p.get("retractions")]
    if leader and downplay:
        evidence=[]
        for sid in leader:
            evidence.append({"session_id":sid,"snippet":session_claims[sid]["snippet"],"confidence":session_claims[sid]["confidence"],"claim_type":"lead"})
        for sid in downplay:
            evidence.append({"session_id":sid,"snippet":session_claims[sid]["snippet"],"confidence":session_claims[sid]["confidence"],"claim_type":"downplay"})
        contradictions.append({"lie_type":"role_contradiction_aggressive","contradictory_claims":["Claimed leadership/seniority","Later hedged/downplayed/admits limited role or copying"],"evidence":evidence})
    # assertion vs retraction (same or cross-session) - aggressive
    asserts=[sid for sid,p in session_claims.items() if p.get("assertions")]
    retracts=[sid for sid,p in session_claims.items() if p.get("retractions") or p.get("neg") or p.get("copy") or p.get("hedges")]
    for a in asserts:
        for r in retracts:
            if a==r:
                contradictions.append({"lie_type":"assertion_retraction_same_session_aggressive","contradictory_claims":[f"{a}: assertion",f"{r}: retraction/hedge"],"evidence":[{"session_id":a,"assertions":session_claims[a]["assertions"],"retractions":session_claims[a]["retractions"],"hedges":session_claims[a]["hedges"],"confidence":session_claims[a]["confidence"]}]})
            else:
                contradictions.append({"lie_type":"assertion_then_retraction_aggressive","contradictory_claims":[f"{a}: assertion",f"{r}: retraction/hedge"],"evidence":[{"session_id":a,"assertions":session_claims[a]["assertions"],"confidence":session_claims[a]["confidence"]},{"session_id":r,"retractions":session_claims[r]["retractions"],"hedges":session_claims[r]["hedges"],"copy":session_claims[r]["copy"],"neg":session_claims[r]["neg"],"confidence":session_claims[r]["confidence"]}]})
    # dedupe by type+claims
    seen=set(); dedup=[]
    for c in contradictions:
        key=(c.get("lie_type"), tuple(c.get("contradictory_claims",[])))
        if key in seen: continue
        seen.add(key); dedup.append(c)
    return dedup

def infer_revealed_truth_aggressive(session_claims):
    all_years=[]; qualifiers=set()
    for p in session_claims.values():
        for y in p.get("years",[]): 
            try: all_years.append(int(y))
            except: pass
        if p.get("neg"): qualifiers.add("includes internships/limited role")
        if p.get("copy"): qualifiers.add("admitted copying/using others' work")
        if p.get("hedges"): qualifiers.add("hedged claims")
    prog_exp="unknown"
    if all_years:
        mn=min(all_years); mx=max(all_years)
        prog_exp = f"{mn} years" + (f" - {mx} years" if mx!=mn else "")
        if qualifiers:
            prog_exp += " (" + "; ".join(sorted(qualifiers)) + ")"
    all_langs=[]
    for p in session_claims.values():
        all_langs.extend([l for l in p.get("languages",[]) if l])
    prog_lang = Counter(all_langs).most_common(1)[0][0] if all_langs else "unknown"
    any_lead = any(p.get("lead") for p in session_claims.values())
    any_copy = any(p.get("copy") for p in session_claims.values())
    any_neg = any(p.get("neg") for p in session_claims.values())
    mastery = "intermediate"
    if any_copy or any_neg or any(p.get("hedges") for p in session_claims.values()):
        mastery = "intermediate"
    elif any_lead and not any_copy:
        mastery = "advanced"
    leadership_claims="none"; team_experience="individual contributor"
    if any_lead and (any_copy or any_neg or any(p.get("hedges") for p in session_claims.values())):
        leadership_claims="fabricated/hedged"
    elif any_lead:
        leadership_claims="claimed"; team_experience="claimed team lead"
    skills=set()
    for p in session_claims.values():
        for s in p.get("skills",[]): skills.add(s.title())
    return {"programming_experience":prog_exp, "programming_language":prog_lang, "skill_mastery":mastery, "leadership_claims":leadership_claims, "team_experience":team_experience, "skills and other keywords":sorted(skills)}

# ---------------------------
# Main: run for each subject and write outputs
# ---------------------------
def build_all(master_path: Path, out_dir: Path):
    parsed = parse_master(master_path)
    out_dir.mkdir(parents=True, exist_ok=True)
    results={}
    for subj,sessions in parsed.items():
        session_claims={}
        for title,block in sessions.items():
            clean = re.sub(r"\s+","_", title).strip()
            sid = subj + "::" + clean
            session_claims[sid] = extract_session_claims(block)
        contradictions = detect_aggressive_contradictions(session_claims)
        revealed = infer_revealed_truth_aggressive(session_claims)
        final = {"shadow_id":subj, "revealed_truth":revealed, "deception_patterns":contradictions}
        (out_dir / f"{subj}.json").write_text(json.dumps(final, indent=2), encoding="utf8")
        (out_dir / f"{subj}_submission_scroll.txt").write_text("SUBMISSION SCROLL\n\n"+json.dumps(final, indent=2), encoding="utf8")
        results[subj]=final
    return results

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--master", required=True, help="Path to MASTER_TRANSCRIPT.txt")
    parser.add_argument("--out", default="out_aggressive", help="output folder")
    args = parser.parse_args()
    master_path = Path(args.master)
    if not master_path.exists():
        raise SystemExit(f"Master transcript not found: {master_path}")
    out_dir = Path(args.out)
    results = build_all(master_path, out_dir)
    print(json.dumps(results, indent=2))
