# Truth Weaver 🕵️‍♂️

A comprehensive audio-to-text transcription and deception analysis system that spots contradictions, highlights unreliable statements, and weaves together the most likely truth from multiple audio sessions.

## 🎯 Overview

Truth Weaver processes multiple audio sessions from subjects and analyzes them for:
- **Experience inflation** (contradictory claims about years of experience)
- **Leadership fabrication** (claiming leadership while admitting to copying work)
- **Skill misrepresentation** (inconsistent technical claims)
- **Team experience contradictions** (claiming team leadership vs. working alone)

## 📁 Project Structure

```
Truth_Weaver/
├── src/
│   └── asr_runner.py              # Audio transcription engine
├── scripts/
│   ├── run_assemblyai.py          # Enhanced AssemblyAI integration
│   └── submission_scroll.py       # Contradiction analysis engine
├── truth_weaver.py                # Main orchestrator
├── batch_processor.py             # Batch processing system
├── validate_output.py             # Output validation tools
├── test_truth_weaver.py           # Test suite
└── audio copy/audio/              # Sample audio files
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install requests pathlib
```

### 2. Set up AssemblyAI API Key (Optional)

```bash
export ASSEMBLYAI_API_KEY="your_api_key_here"
```

*Note: A fallback API key is included for testing, but setting your own is recommended.*

### 3. Run a Quick Test

```bash
python test_truth_weaver.py --quick-test
```

### 4. Process a Single Subject

```bash
python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio" --output-dir output
```

### 5. Process All Subjects in Batch

```bash
python batch_processor.py --audio-dir "audio copy/audio" --output-dir output --workers 4
```

## 📊 Output Format

### JSON Output (`subject_name.json`)

```json
{
  "shadow_id": "phoenix_2024",
  "revealed_truth": {
    "programming_experience": "3 years",
    "programming_language": "python",
    "skill_mastery": "intermediate",
    "leadership_claims": "fabricated",
    "team_experience": "individual contributor",
    "skills and other keywords": ["Machine Learning", "Deep Learning"]
  },
  "deception_patterns": [
    {
      "lie_type": "experience_inflation",
      "contradictory_claims": ["6 years", "3 years", "2 months"]
    },
    {
      "lie_type": "leadership_fabrication", 
      "contradictory_claims": [
        "Claimed leadership and team management",
        "Admitted working alone and copying others' work"
      ]
    }
  ]
}
```

### Transcript Output (`subject_name_MASTER_TRANSCRIPT.txt`)

```
SESSION 1: Session 1
==================================================

FULL TEXT:
I've mastered Python for 6 years... built incredible systems...

TIMESTAMPED BREAKDOWN:
[0.0s - 3.2s] I've mastered Python for 6 years
[3.2s - 6.8s] built incredible systems from scratch
...

Overall Confidence: 87.3%

SESSION 2: Session 2
==================================================
...
```

## 🛠️ Available Tools

### Main Processing Tools

| Tool | Purpose | Usage |
|------|---------|-------|
| `truth_weaver.py` | Single subject processing | `python truth_weaver.py --subject name --audio-dir path` |
| `batch_processor.py` | Batch processing | `python batch_processor.py --audio-dir path --workers 4` |

### Utility Tools

| Tool | Purpose | Usage |
|------|---------|-------|
| `validate_output.py` | Validate outputs | `python validate_output.py --json-file output.json` |
| `test_truth_weaver.py` | Run tests | `python test_truth_weaver.py --quick-test` |

### Backend Options

- **`assemblyai`** (default): High-quality transcription using AssemblyAI API
- **`stub`**: Offline testing using pre-created transcript files

## 🔍 Analysis Features

### Contradiction Detection

1. **Experience Inflation**: Detects inconsistent claims about years of experience
2. **Leadership Fabrication**: Identifies contradictions between leadership claims and admissions of copying
3. **Skill Misrepresentation**: Analyzes technical skill claims for consistency

### Truth Extraction

- **Conservative Estimation**: Uses lower-bound estimates when contradictions exist
- **Evidence Weighting**: Prioritizes more reliable statements based on confidence scores
- **Pattern Recognition**: Identifies common deception patterns across sessions

## 📈 Evaluation Metrics

The system is evaluated on two metrics:

1. **Transcript Accuracy**: Character similarity using normalized Levenshtein distance
2. **Truth Extraction Accuracy**: Jaccard similarity score for JSON fields

## 🧪 Testing

### Quick Test (Stub Backend)
```bash
python test_truth_weaver.py --quick-test
```

### Full Test (Real Audio)
```bash
python test_truth_weaver.py --full-test
```

### Create Sample Data
```bash
python test_truth_weaver.py --create-sample-data
```

## 📋 Validation

Validate your outputs to ensure they meet the required format:

```bash
# Validate single files
python validate_output.py --json-file output/subject.json
python validate_output.py --txt-file output/subject_MASTER_TRANSCRIPT.txt

# Validate batch results
python validate_output.py --batch-dir output/batch_20241216_143022
```

## 🔧 Configuration

### Audio File Naming Convention

Audio files should follow the pattern: `{subject_name}_{session_number}.mp3`

Examples:
- `atlas_2025_1.mp3`
- `atlas_2025_2.mp3`
- `phoenix_2024_1.mp3`

### Environment Variables

- `ASSEMBLYAI_API_KEY`: Your AssemblyAI API key (optional, fallback provided)

## 📊 Batch Processing Features

- **Parallel Processing**: Process multiple subjects simultaneously
- **Progress Tracking**: Real-time progress updates
- **Comprehensive Reporting**: Detailed batch summary with statistics
- **Error Handling**: Graceful handling of individual subject failures

## 🎯 Sample Results

The system successfully identifies patterns like:

- Subject claims "6 years experience" in Session 1, then "3 years" in Session 2, finally "2 months" in Session 5
- Claims to "lead a team of five" while also admitting to "work alone mostly"
- Technical inconsistencies and skill misrepresentations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is part of the INNOV8 Truth Weaver challenge.

---

*Truth Weaver: Separating fact from fiction, one audio session at a time.* 🎭
