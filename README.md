# Truth Weaver 🕵️‍♂️

A comprehensive audio-to-text transcription and deception analysis system that spots contradictions, highlights unreliable statements, and weaves together the most likely truth from multiple audio sessions.

## 🎯 Overview

Truth Weaver processes multiple audio sessions from subjects and analyzes them for:
- **Experience inflation** (contradictory claims about years of experience)
- **Leadership fabrication** (claiming leadership while admitting to copying work)
- **Skill misrepresentation** (inconsistent technical claims)
- **Team experience contradictions** (claiming team leadership vs. working alone)

## 📁 Project Structure

```
Truth_Weaver/
├── src/
│   └── asr_runner.py              # Audio transcription engine
├── scripts/
│   ├── run_assemblyai.py          # Enhanced AssemblyAI integration
│   └── submission_scroll.py       # Contradiction analysis engine
├── truth_weaver.py                # Main orchestrator
├── batch_processor.py             # Batch processing system
├── validate_output.py             # Output validation tools
├── test_truth_weaver.py           # Test suite
└── audio copy/audio/              # Sample audio files
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install requests pathlib
```

### 2. Set up AssemblyAI API Key (Optional)

```bash
export ASSEMBLYAI_API_KEY="your_api_key_here"
```

### 3. Run a Quick Test

```bash
python test_truth_weaver.py --quick-test
```

### 4. Process a Single Subject

```bash
python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio" --output-dir output
```

### 5. Process All Subjects in Batch

```bash
python batch_processor.py --audio-dir "audio copy/audio" --output-dir output --workers 4
```

## 📊 Output Format

### JSON Output (`subject_name.json`)

```json
{
  "shadow_id": "phoenix_2024",
  "revealed_truth": {
    "programming_experience": "3 years",
    "programming_language": "python",
    "skill_mastery": "intermediate",
    "leadership_claims": "fabricated",
    "team_experience": "individual contributor",
    "skills and other keywords": ["Machine Learning", "Deep Learning"]
  },
  "deception_patterns": [
    {
      "lie_type": "experience_inflation",
      "contradictory_claims": ["6 years", "3 years", "2 months"]
    },
    {
      "lie_type": "leadership_fabrication", 
      "contradictory_claims": [
        "Claimed leadership and team management",
        "Admitted working alone and copying others' work"
      ]
    }
  ]
}
```

### Transcript Output (`subject_name_MASTER_TRANSCRIPT.txt`)

```
SESSION 1: Session 1
==================================================

FULL TEXT:
I've mastered Python for 6 years... built incredible systems...

TIMESTAMPED BREAKDOWN:
[0.0s - 3.2s] I've mastered Python for 6 years
[3.2s - 6.8s] built incredible systems from scratch
...

Overall Confidence: 87.3%

SESSION 2: Session 2
==================================================
...
```

## 🛠️ Available Tools

### Main Processing Tools

| Tool | Purpose | Usage |
|------|---------|-------|
| `truth_weaver.py` | Single subject processing | `python truth_weaver.py --subject name --audio-dir path` |
| `batch_processor.py` | Batch processing | `python batch_processor.py --audio-dir path --workers 4` |

### Utility Tools

| Tool | Purpose | Usage |
|------|---------|-------|
| `validate_output.py` | Validate outputs | `python validate_output.py --json-file output.json` |
| `test_truth_weaver.py` | Run tests | `python test_truth_weaver.py --quick-test` |

### Backend Options

- **`assemblyai`** (default): High-quality transcription using AssemblyAI API
- **`stub`**: Offline testing using pre-created transcript files

## 🔍 Technical Approach

### Audio-to-Text Processing

**AssemblyAI Integration:**
- **High-Quality Transcription**: Leverages AssemblyAI's advanced speech recognition API for accurate transcription
- **Speaker Diarization**: Automatically identifies and separates different speakers in multi-speaker scenarios
- **Confidence Scoring**: Each transcribed segment includes confidence scores (0-100%) for reliability assessment
- **Timestamped Output**: Provides precise timing information for each spoken segment
- **Noise Handling**: Robust performance across varying audio quality conditions

**Processing Pipeline:**
1. **Audio Upload**: Secure upload to AssemblyAI's processing servers
2. **Real-time Processing**: Asynchronous transcription with status polling
3. **Quality Assessment**: Confidence score analysis and low-quality segment identification
4. **Structured Output**: Organized session-by-session transcript generation

### Deception Detection Techniques

**Aggressive Pattern Recognition:**
The system employs sophisticated linguistic analysis to identify deception patterns:

**1. Experience Inflation Detection**
- **Numeric Extraction**: Regex patterns to extract years of experience from natural language
- **Cross-Session Comparison**: Identifies any differing numeric claims across sessions
- **Temporal Inconsistencies**: Flags contradictory timeline claims (e.g., "7 years" → "2 years")

**2. Assertion-Retraction Analysis**
- **Assertive Verb Detection**: Identifies confident claims using patterns like "I built", "I designed", "I architected"
- **Retraction Pattern Recognition**: Detects backtracking with phrases like "should correct", "too strong", "not the architect"
- **Cross-Session Contradictions**: Maps assertions in early sessions against retractions in later sessions
- **Same-Session Contradictions**: Identifies internal contradictions within single conversations

**3. Leadership Fabrication Detection**
- **Leadership Keywords**: Detects claims using "lead", "manage", "team", "architect", "senior"
- **Downplay Patterns**: Identifies hedging with "mostly", "just", "only", "internship"
- **Copy Admission Detection**: Flags admissions of using others' work or following instructions
- **Role Contradiction Mapping**: Cross-references leadership claims against admissions of limited responsibility

**4. Hedging and Uncertainty Analysis**
- **Hedging Patterns**: Detects uncertainty markers like "I think", "maybe", "probably"
- **Negative Admissions**: Identifies direct contradictions like "I'm not a", "just want to be"
- **Qualification Detection**: Recognizes statements that diminish previous claims

**Advanced Analysis Features:**

**Evidence Weighting System:**
- **Confidence Score Integration**: Higher confidence transcriptions weighted more heavily
- **Session Progression Analysis**: Later sessions often contain more truthful admissions
- **Contradiction Severity Scoring**: Multiple contradictions increase deception likelihood

**Truth Extraction Algorithm:**
- **Conservative Estimation**: When contradictions exist, uses minimum claimed values
- **Qualifier Integration**: Adds context like "includes internships" or "admitted copying"
- **Skill Mastery Assessment**: Downgrades claims when contradictions or hedging detected
- **Leadership Classification**: Distinguishes between "claimed", "fabricated/hedged", and "none"

**Pattern Matching Engine:**
```python
ASSERTIVE_VERBS = [r"\bi built\b", r"\bi designed\b", r"\bi architected\b"]
RETRACTION_PATTERNS = [r"\bshould correct\b", r"\btoo strong\b", r"\bnot the architect\b"]
HEDGING = [r"\bmostly\b", r"\bjust\b", r"\bonly\b"]
NEGATIVE_ADMISSION = [r"\bi'm not an?\b", r"\bjust want to be\b", r"\binternship\b"]
```

### Analysis Features

**Multi-Dimensional Contradiction Detection:**
1. **Experience Inflation**: Detects inconsistent claims about years of experience using aggressive numeric comparison
2. **Leadership Fabrication**: Identifies contradictions between leadership claims and admissions of copying/limited role
3. **Skill Misrepresentation**: Analyzes technical skill claims for consistency across sessions
4. **Role Contradiction**: Maps senior role claims against junior role admissions

**Truth Extraction Methodology:**
- **Conservative Estimation**: Uses lower-bound estimates when contradictions exist
- **Evidence Weighting**: Prioritizes more reliable statements based on confidence scores and session progression
- **Pattern Recognition**: Identifies common deception patterns using linguistic analysis
- **Contextual Qualification**: Adds important context to extracted truths (e.g., "includes internships")

## 🎯 System Performance

### Detection Effectiveness

**Aggressive Analysis Results:**
- **Detection Rate**: 62.5% of subjects show contradictions (5 out of 8 subjects)
- **Total Contradictions**: 18 deception patterns identified across all subjects
- **Pattern Distribution**:
  - Role Contradiction Aggressive: 5 subjects
  - Assertion-Retraction Aggressive: 10 instances
  - Same-Session Contradictions: 2 instances
  - Experience Inflation: 1 subject

**Sample Detection Success:**
- **hyperion_2022**: 10 deception patterns detected
  - "I built my backend from scratch" → "I just used his transaction decorator"
- **atlas_2025**: 2 deception patterns detected
  - "I wrote all policies from scratch" → "It was just a summer internship"
- **titan_2023**: 3 deception patterns detected
  - Experience inflation: "7 years" → "2 years"

### Processing Performance

**Batch Processing Capabilities:**
- **Parallel Processing**: Multi-threaded processing with configurable worker count
- **Processing Speed**: ~71.7 seconds for 8 subjects with 37 audio sessions
- **Success Rate**: 100% processing success rate across all subjects
- **Scalability**: Handles large batches with comprehensive error handling

## 📈 Evaluation Metrics

The system is evaluated on two metrics:

1. **Transcript Accuracy**: Character similarity using normalized Levenshtein distance
2. **Truth Extraction Accuracy**: Jaccard similarity score for JSON fields

### Validation Compliance
- **Format Validation**: 100% compliance with required JSON schema
- **Field Completeness**: All required fields properly populated
- **Data Integrity**: Consistent data types and value ranges maintained

## 🧪 Testing

### Quick Test (Stub Backend)
```bash
python test_truth_weaver.py --quick-test
```

### Full Test (Real Audio)
```bash
python test_truth_weaver.py --full-test
```

### Create Sample Data
```bash
python test_truth_weaver.py --create-sample-data
```

## 📋 Validation

Validate your outputs to ensure they meet the required format:

```bash
# Validate single files
python validate_output.py --json-file output/subject.json
python validate_output.py --txt-file output/subject_MASTER_TRANSCRIPT.txt

# Validate batch results
python validate_output.py --batch-dir output/batch_20241216_143022
```

## 🔧 Configuration

### Audio File Naming Convention

Audio files should follow the pattern: `{subject_name}_{session_number}.mp3`

Examples:
- `atlas_2025_1.mp3`
- `atlas_2025_2.mp3`
- `phoenix_2024_1.mp3`

### Environment Variables

- `ASSEMBLYAI_API_KEY`: Your AssemblyAI API key (optional, fallback provided)

## 📊 Batch Processing Features

- **Parallel Processing**: Process multiple subjects simultaneously
- **Progress Tracking**: Real-time progress updates
- **Comprehensive Reporting**: Detailed batch summary with statistics
- **Error Handling**: Graceful handling of individual subject failures

## 🎯 Sample Results

The system successfully identifies patterns like:

- Subject claims "6 years experience" in Session 1, then "3 years" in Session 2, finally "2 months" in Session 5
- Claims to "lead a team of five" while also admitting to "work alone mostly"
- Technical inconsistencies and skill misrepresentations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

