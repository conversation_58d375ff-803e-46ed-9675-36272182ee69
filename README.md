# Truth Weaver 🕵️‍♂️

A comprehensive audio-to-text transcription and deception analysis system that spots contradictions, highlights unreliable statements, and weaves together the most likely truth from multiple audio sessions.

## 🎯 Overview

Truth Weaver processes multiple audio sessions from subjects and analyzes them for:
- **Experience inflation** (contradictory claims about years of experience)
- **Leadership fabrication** (claiming leadership while admitting to copying work)
- **Skill misrepresentation** (inconsistent technical claims)
- **Team experience contradictions** (claiming team leadership vs. working alone)

## 📁 Project Structure

```
Truth_Weaver/
├── src/
│   └── asr_runner.py              # Audio transcription engine
├── scripts/
│   ├── run_assemblyai.py          # Enhanced AssemblyAI integration
│   └── submission_scroll.py       # Contradiction analysis engine
├── truth_weaver.py                # Main orchestrator
├── batch_processor.py             # Batch processing system
├── validate_output.py             # Output validation tools
├── test_truth_weaver.py           # Test suite
└── audio copy/audio/              # Sample audio files
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install requests pathlib
```

### 2. Set up AssemblyAI API Key (Optional)

```bash
export ASSEMBLYAI_API_KEY="your_api_key_here"
```

### 3. Run a Quick Test

```bash
python test_truth_weaver.py --quick-test
```

### 4. Process a Single Subject

```bash
python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio" --output-dir output
```

### 5. Process All Subjects in Batch

```bash
python batch_processor.py --audio-dir "audio copy/audio" --output-dir output --workers 4
```

## 📊 Output Format

### JSON Output (`subject_name.json`)

```json
{
  "shadow_id": "phoenix_2024",
  "revealed_truth": {
    "programming_experience": "3 years",
    "programming_language": "python",
    "skill_mastery": "intermediate",
    "leadership_claims": "fabricated",
    "team_experience": "individual contributor",
    "skills and other keywords": ["Machine Learning", "Deep Learning"]
  },
  "deception_patterns": [
    {
      "lie_type": "experience_inflation",
      "contradictory_claims": ["6 years", "3 years", "2 months"]
    },
    {
      "lie_type": "leadership_fabrication", 
      "contradictory_claims": [
        "Claimed leadership and team management",
        "Admitted working alone and copying others' work"
      ]
    }
  ]
}
```

### Transcript Output (`subject_name_MASTER_TRANSCRIPT.txt`)

```
SESSION 1: Session 1
==================================================

FULL TEXT:
I've mastered Python for 6 years... built incredible systems...

TIMESTAMPED BREAKDOWN:
[0.0s - 3.2s] I've mastered Python for 6 years
[3.2s - 6.8s] built incredible systems from scratch
...

Overall Confidence: 87.3%

SESSION 2: Session 2
==================================================
...
```

## 🛠️ Available Tools

### Main Processing Tools

| Tool | Purpose | Usage |
|------|---------|-------|
| `truth_weaver.py` | Single subject processing | `python truth_weaver.py --subject name --audio-dir path` |
| `batch_processor.py` | Batch processing | `python batch_processor.py --audio-dir path --workers 4` |

### Utility Tools

| Tool | Purpose | Usage |
|------|---------|-------|
| `validate_output.py` | Validate outputs | `python validate_output.py --json-file output.json` |
| `test_truth_weaver.py` | Run tests | `python test_truth_weaver.py --quick-test` |

### Backend Options

- **`assemblyai`** (default): High-quality transcription using AssemblyAI API
- **`stub`**: Offline testing using pre-created transcript files

## 🔍 Technical Approach

### Audio-to-Text Processing
- **AssemblyAI Integration**: High-quality transcription with speaker diarization and confidence scoring
- **Processing Pipeline**: Audio upload → Real-time transcription → Quality assessment → Structured output

### Deception Detection Techniques

**Pattern Recognition Engine:**
```python
ASSERTIVE_VERBS = [r"\bi built\b", r"\bi designed\b", r"\bi architected\b"]
RETRACTION_PATTERNS = [r"\bshould correct\b", r"\btoo strong\b", r"\bnot the architect\b"]
HEDGING = [r"\bmostly\b", r"\bjust\b", r"\bonly\b"]
NEGATIVE_ADMISSION = [r"\bi'm not an?\b", r"\bjust want to be\b", r"\binternship\b"]
```

**Detection Methods:**
1. **Experience Inflation**: Cross-session numeric comparison (e.g., "7 years" → "2 years")
2. **Assertion-Retraction**: Maps confident claims against later backtracking
3. **Leadership Fabrication**: Leadership claims vs. admissions of copying/limited role
4. **Hedging Analysis**: Uncertainty markers and negative admissions

**Truth Extraction:**
- Conservative estimation using minimum claimed values
- Evidence weighting based on confidence scores
- Contextual qualifiers (e.g., "includes internships")

## 📊 Performance Results

**Processing Speed:**
- 71.7 seconds for 8 subjects (37 audio sessions)
- 100% success rate with parallel processing

## 📈 Evaluation Metrics

1. **Transcript Accuracy**: Character similarity (Levenshtein distance)
2. **Truth Extraction Accuracy**: Jaccard similarity for JSON fields

## 🧪 Testing

### Quick Test (Stub Backend)
```bash
python test_truth_weaver.py --quick-test
```

### Full Test (Real Audio)
```bash
python test_truth_weaver.py --full-test
```

### Create Sample Data
```bash
python test_truth_weaver.py --create-sample-data
```

## 📋 Validation

Validate your outputs to ensure they meet the required format:

```bash
# Validate single files
python validate_output.py --json-file output/subject.json
python validate_output.py --txt-file output/subject_MASTER_TRANSCRIPT.txt

# Validate batch results
python validate_output.py --batch-dir output/batch_20241216_143022
```

## 🔧 Configuration

### Audio File Naming Convention

Audio files should follow the pattern: `{subject_name}_{session_number}.mp3`

Examples:
- `atlas_2025_1.mp3`
- `atlas_2025_2.mp3`
- `phoenix_2024_1.mp3`

### Environment Variables

- `ASSEMBLYAI_API_KEY`: Your AssemblyAI API key (optional, fallback provided)

## 📊 Batch Processing Features

- **Parallel Processing**: Process multiple subjects simultaneously
- **Progress Tracking**: Real-time progress updates
- **Comprehensive Reporting**: Detailed batch summary with statistics
- **Error Handling**: Graceful handling of individual subject failures

## 🎯 Sample Results

The system successfully identifies patterns like:

- Subject claims "6 years experience" in Session 1, then "3 years" in Session 2, finally "2 months" in Session 5
- Claims to "lead a team of five" while also admitting to "work alone mostly"
- Technical inconsistencies and skill misrepresentations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

