#!/usr/bin/env python3
"""
Clean Transcript Formatter
==========================

Converts the clean master transcript to:
- All lowercase letters
- Remove all punctuation
- Keep only lowercase alphabets and spaces

Usage:
    python clean_transcript_formatter.py --input CLEAN_MASTER_TRANSCRIPT.txt --output CLEAN_FORMATTED_TRANSCRIPT.txt
"""

import argparse
import re
from pathlib import Path


def clean_and_format_transcript(input_file: Path, output_file: Path):
    """Clean transcript by converting to lowercase and removing punctuation."""
    
    print(f"Reading transcript from: {input_file}")
    
    # Read the input file
    content = input_file.read_text(encoding="utf-8")
    
    # Split into lines
    lines = content.split('\n')
    
    cleaned_lines = []
    
    for line in lines:
        if line.strip():  # Skip empty lines
            # Convert to lowercase
            line_lower = line.lower()
            
            # Remove all punctuation, keep only letters and spaces
            # This regex keeps only lowercase letters, spaces, and the colon after filename
            cleaned_line = re.sub(r'[^a-z\s:]', '', line_lower)
            
            # Clean up multiple spaces
            cleaned_line = re.sub(r'\s+', ' ', cleaned_line)
            
            # Strip leading/trailing spaces
            cleaned_line = cleaned_line.strip()
            
            if cleaned_line:  # Only add non-empty lines
                cleaned_lines.append(cleaned_line)
        else:
            # Preserve empty lines for formatting
            cleaned_lines.append('')
    
    # Join lines back together
    cleaned_content = '\n'.join(cleaned_lines)
    
    # Write to output file
    output_file.write_text(cleaned_content, encoding="utf-8")
    
    print(f"Cleaned transcript saved to: {output_file}")
    print(f"Original lines: {len(lines)}")
    print(f"Cleaned lines: {len(cleaned_lines)}")


def main():
    parser = argparse.ArgumentParser(
        description="Clean and format transcript to lowercase without punctuation",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--input", default="CLEAN_MASTER_TRANSCRIPT.txt",
                       help="Input transcript file")
    parser.add_argument("--output", default="CLEAN_FORMATTED_TRANSCRIPT.txt",
                       help="Output cleaned transcript file")
    
    args = parser.parse_args()
    
    input_file = Path(args.input)
    if not input_file.exists():
        print(f"Input file does not exist: {input_file}")
        return 1
    
    output_file = Path(args.output)
    
    try:
        clean_and_format_transcript(input_file, output_file)
        print("Transcript cleaning completed successfully!")
        return 0
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
