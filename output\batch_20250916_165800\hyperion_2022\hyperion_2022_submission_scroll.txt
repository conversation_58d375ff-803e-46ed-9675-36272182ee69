SUBMISSION SCROLL (AGGRESSIVE ANALYSIS)
==================================================

Subject: hyperion_2022

REVEALED TRUTH:
  programming_experience: unknown
  programming_language: unknown
  skill_mastery: intermediate
  leadership_claims: fabricated/hedged
  team_experience: individual contributor
  skills and other keywords: ['Model']

DECEPTION PATTERNS:
- lie_type: role_contradiction_aggressive
  contradictory_claims: ['Claimed leadership/seniority', 'Later hedged/downplayed/admits limited role or copying']
  evidence:
    - {'session_id': 'hyperion_2022::session_3', 'snippet': 'I the lead dev actually wrote a library for that.', 'confidence': 0.997, 'claim_type': 'lead'}
    - {'session_id': 'hyperion_2022::session_5', 'snippet': 'I wrote the code for it.', 'confidence': 0.9890000000000001, 'claim_type': 'lead'}
    - {'session_id': 'hyperion_2022::session_3', 'snippet': 'I the lead dev actually wrote a library for that.', 'confidence': 0.997, 'claim_type': 'downplay'}
    - {'session_id': 'hyperion_2022::session_4', 'snippet': 'I wrote all the business logic.', 'confidence': 0.993, 'claim_type': 'downplay'}
    - {'session_id': 'hyperion_2022::session_5', 'snippet': 'I wrote the code for it.', 'confidence': 0.9890000000000001, 'claim_type': 'downplay'}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['hyperion_2022::session_1: assertion', 'hyperion_2022::session_3: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_1', 'assertions': ['I built my backend from the very first line of code.'], 'confidence': 0.995}
    - {'session_id': 'hyperion_2022::session_3', 'retractions': ['I the lead dev actually wrote a library for that.', 'I just used his transaction decorator.'], 'hedges': [], 'copy': True, 'neg': False, 'confidence': 0.997}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['hyperion_2022::session_1: assertion', 'hyperion_2022::session_4: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_1', 'assertions': ['I built my backend from the very first line of code.'], 'confidence': 0.995}
    - {'session_id': 'hyperion_2022::session_4', 'retractions': ['He just provided some.'], 'hedges': [], 'copy': False, 'neg': False, 'confidence': 0.993}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['hyperion_2022::session_1: assertion', 'hyperion_2022::session_5: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_1', 'assertions': ['I built my backend from the very first line of code.'], 'confidence': 0.995}
    - {'session_id': 'hyperion_2022::session_5', 'retractions': ['He designed the core architecture and the database schema.', 'I was the lead developer, not the architect.'], 'hedges': [], 'copy': False, 'neg': False, 'confidence': 0.9890000000000001}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['hyperion_2022::session_4: assertion', 'hyperion_2022::session_3: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_4', 'assertions': ['I wrote all the business logic.'], 'confidence': 0.993}
    - {'session_id': 'hyperion_2022::session_3', 'retractions': ['I the lead dev actually wrote a library for that.', 'I just used his transaction decorator.'], 'hedges': [], 'copy': True, 'neg': False, 'confidence': 0.997}

- lie_type: assertion_retraction_same_session_aggressive
  contradictory_claims: ['hyperion_2022::session_4: assertion', 'hyperion_2022::session_4: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_4', 'assertions': ['I wrote all the business logic.'], 'retractions': ['He just provided some.'], 'hedges': [], 'confidence': 0.993}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['hyperion_2022::session_4: assertion', 'hyperion_2022::session_5: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_4', 'assertions': ['I wrote all the business logic.'], 'confidence': 0.993}
    - {'session_id': 'hyperion_2022::session_5', 'retractions': ['He designed the core architecture and the database schema.', 'I was the lead developer, not the architect.'], 'hedges': [], 'copy': False, 'neg': False, 'confidence': 0.9890000000000001}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['hyperion_2022::session_5: assertion', 'hyperion_2022::session_3: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_5', 'assertions': ['I wrote the code for it.', 'I was the lead developer, not the architect.'], 'confidence': 0.9890000000000001}
    - {'session_id': 'hyperion_2022::session_3', 'retractions': ['I the lead dev actually wrote a library for that.', 'I just used his transaction decorator.'], 'hedges': [], 'copy': True, 'neg': False, 'confidence': 0.997}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['hyperion_2022::session_5: assertion', 'hyperion_2022::session_4: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_5', 'assertions': ['I wrote the code for it.', 'I was the lead developer, not the architect.'], 'confidence': 0.9890000000000001}
    - {'session_id': 'hyperion_2022::session_4', 'retractions': ['He just provided some.'], 'hedges': [], 'copy': False, 'neg': False, 'confidence': 0.993}

- lie_type: assertion_retraction_same_session_aggressive
  contradictory_claims: ['hyperion_2022::session_5: assertion', 'hyperion_2022::session_5: retraction/hedge']
  evidence:
    - {'session_id': 'hyperion_2022::session_5', 'assertions': ['I wrote the code for it.', 'I was the lead developer, not the architect.'], 'retractions': ['He designed the core architecture and the database schema.', 'I was the lead developer, not the architect.'], 'hedges': [], 'confidence': 0.9890000000000001}


FULL JSON:
{
  "shadow_id": "hyperion_2022",
  "revealed_truth": {
    "programming_experience": "unknown",
    "programming_language": "unknown",
    "skill_mastery": "intermediate",
    "leadership_claims": "fabricated/hedged",
    "team_experience": "individual contributor",
    "skills and other keywords": [
      "Model"
    ]
  },
  "deception_patterns": [
    {
      "lie_type": "role_contradiction_aggressive",
      "contradictory_claims": [
        "Claimed leadership/seniority",
        "Later hedged/downplayed/admits limited role or copying"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_3",
          "snippet": "I the lead dev actually wrote a library for that.",
          "confidence": 0.997,
          "claim_type": "lead"
        },
        {
          "session_id": "hyperion_2022::session_5",
          "snippet": "I wrote the code for it.",
          "confidence": 0.9890000000000001,
          "claim_type": "lead"
        },
        {
          "session_id": "hyperion_2022::session_3",
          "snippet": "I the lead dev actually wrote a library for that.",
          "confidence": 0.997,
          "claim_type": "downplay"
        },
        {
          "session_id": "hyperion_2022::session_4",
          "snippet": "I wrote all the business logic.",
          "confidence": 0.993,
          "claim_type": "downplay"
        },
        {
          "session_id": "hyperion_2022::session_5",
          "snippet": "I wrote the code for it.",
          "confidence": 0.9890000000000001,
          "claim_type": "downplay"
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_1: assertion",
        "hyperion_2022::session_3: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_1",
          "assertions": [
            "I built my backend from the very first line of code."
          ],
          "confidence": 0.995
        },
        {
          "session_id": "hyperion_2022::session_3",
          "retractions": [
            "I the lead dev actually wrote a library for that.",
            "I just used his transaction decorator."
          ],
          "hedges": [],
          "copy": true,
          "neg": false,
          "confidence": 0.997
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_1: assertion",
        "hyperion_2022::session_4: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_1",
          "assertions": [
            "I built my backend from the very first line of code."
          ],
          "confidence": 0.995
        },
        {
          "session_id": "hyperion_2022::session_4",
          "retractions": [
            "He just provided some."
          ],
          "hedges": [],
          "copy": false,
          "neg": false,
          "confidence": 0.993
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_1: assertion",
        "hyperion_2022::session_5: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_1",
          "assertions": [
            "I built my backend from the very first line of code."
          ],
          "confidence": 0.995
        },
        {
          "session_id": "hyperion_2022::session_5",
          "retractions": [
            "He designed the core architecture and the database schema.",
            "I was the lead developer, not the architect."
          ],
          "hedges": [],
          "copy": false,
          "neg": false,
          "confidence": 0.9890000000000001
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_4: assertion",
        "hyperion_2022::session_3: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_4",
          "assertions": [
            "I wrote all the business logic."
          ],
          "confidence": 0.993
        },
        {
          "session_id": "hyperion_2022::session_3",
          "retractions": [
            "I the lead dev actually wrote a library for that.",
            "I just used his transaction decorator."
          ],
          "hedges": [],
          "copy": true,
          "neg": false,
          "confidence": 0.997
        }
      ]
    },
    {
      "lie_type": "assertion_retraction_same_session_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_4: assertion",
        "hyperion_2022::session_4: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_4",
          "assertions": [
            "I wrote all the business logic."
          ],
          "retractions": [
            "He just provided some."
          ],
          "hedges": [],
          "confidence": 0.993
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_4: assertion",
        "hyperion_2022::session_5: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_4",
          "assertions": [
            "I wrote all the business logic."
          ],
          "confidence": 0.993
        },
        {
          "session_id": "hyperion_2022::session_5",
          "retractions": [
            "He designed the core architecture and the database schema.",
            "I was the lead developer, not the architect."
          ],
          "hedges": [],
          "copy": false,
          "neg": false,
          "confidence": 0.9890000000000001
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_5: assertion",
        "hyperion_2022::session_3: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_5",
          "assertions": [
            "I wrote the code for it.",
            "I was the lead developer, not the architect."
          ],
          "confidence": 0.9890000000000001
        },
        {
          "session_id": "hyperion_2022::session_3",
          "retractions": [
            "I the lead dev actually wrote a library for that.",
            "I just used his transaction decorator."
          ],
          "hedges": [],
          "copy": true,
          "neg": false,
          "confidence": 0.997
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_5: assertion",
        "hyperion_2022::session_4: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_5",
          "assertions": [
            "I wrote the code for it.",
            "I was the lead developer, not the architect."
          ],
          "confidence": 0.9890000000000001
        },
        {
          "session_id": "hyperion_2022::session_4",
          "retractions": [
            "He just provided some."
          ],
          "hedges": [],
          "copy": false,
          "neg": false,
          "confidence": 0.993
        }
      ]
    },
    {
      "lie_type": "assertion_retraction_same_session_aggressive",
      "contradictory_claims": [
        "hyperion_2022::session_5: assertion",
        "hyperion_2022::session_5: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "hyperion_2022::session_5",
          "assertions": [
            "I wrote the code for it.",
            "I was the lead developer, not the architect."
          ],
          "retractions": [
            "He designed the core architecture and the database schema.",
            "I was the lead developer, not the architect."
          ],
          "hedges": [],
          "confidence": 0.9890000000000001
        }
      ]
    }
  ]
}