{"master_analysis": {"timestamp": "2025-09-17T14:53:25.844945", "total_subjects": 8, "batch_directory": "output\\batch_20250916_165800"}, "subjects": {"atlas_2025": {"shadow_id": "atlas_2025", "revealed_truth": {"programming_experience": "1 years (admitted copying/using others' work; hedged claims; includes internships/limited role)", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "fabricated/hedged", "team_experience": "individual contributor", "skills and other keywords": []}, "deception_patterns": [{"lie_type": "role_contradiction_aggressive", "contradictory_claims": ["Claimed leadership/seniority", "Later hedged/downplayed/admits limited role or copying"], "evidence": [{"session_id": "atlas_2025::session_4", "snippet": "The senior engineer usually handles the network debugging.", "confidence": 0.998, "claim_type": "lead"}, {"session_id": "atlas_2025::session_5", "snippet": "Okay.", "confidence": 0.978, "claim_type": "lead"}, {"session_id": "atlas_2025::session_5", "snippet": "Okay.", "confidence": 0.978, "claim_type": "downplay"}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["atlas_2025::session_2: assertion", "atlas_2025::session_5: retraction/hedge"], "evidence": [{"session_id": "atlas_2025::session_2", "assertions": ["I wrote all our policies from scratch to ensure total service isolation."], "confidence": 0.995}, {"session_id": "atlas_2025::session_5", "retractions": ["It was an internship.", "It was a summer internship, and I mostly just watched the senior engineers work.", "I ran some scripts they gave me.", "I'm not a DevOps engineer.", "I just want to be one."], "hedges": ["It was a summer internship, and I mostly just watched the senior engineers work."], "copy": true, "neg": true, "confidence": 0.978}]}]}, "crius_2025": {"shadow_id": "crius_2025", "revealed_truth": {"programming_experience": "unknown", "programming_language": "unknown", "skill_mastery": "advanced", "leadership_claims": "claimed", "team_experience": "claimed team lead", "skills and other keywords": []}, "deception_patterns": []}, "eos_2023": {"shadow_id": "eos_2023", "revealed_truth": {"programming_experience": "8 years (hedged claims)", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "fabricated/hedged", "team_experience": "individual contributor", "skills and other keywords": []}, "deception_patterns": [{"lie_type": "role_contradiction_aggressive", "contradictory_claims": ["Claimed leadership/seniority", "Later hedged/downplayed/admits limited role or copying"], "evidence": [{"session_id": "eos_2023::session_1", "snippet": "I'm a principal Cware engineer.", "confidence": 0.97, "claim_type": "lead"}, {"session_id": "eos_2023::session_3", "snippet": "Ah, yes.", "confidence": 0.997, "claim_type": "lead"}, {"session_id": "eos_2023::session_5", "snippet": "While I'm proud of my service level design skills, my goal is to grow into that lead architect role.", "confidence": 0.996, "claim_type": "lead"}, {"session_id": "eos_2023::session_3", "snippet": "Ah, yes.", "confidence": 0.997, "claim_type": "downplay"}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["eos_2023::session_4: assertion", "eos_2023::session_3: retraction/hedge"], "evidence": [{"session_id": "eos_2023::session_4", "assertions": ["I wrote a detailed design document justifying the cost by showing it would save us three months of engineering effort trying to build our own solution."], "confidence": 0.985}, {"session_id": "eos_2023::session_3", "retractions": ["I should correct that wording Architected is too strong."], "hedges": ["I should correct that wording Architected is too strong."], "copy": false, "neg": false, "confidence": 0.997}]}]}, "hyperion_2022": {"shadow_id": "hyperion_2022", "revealed_truth": {"programming_experience": "unknown", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "fabricated/hedged", "team_experience": "individual contributor", "skills and other keywords": ["Model"]}, "deception_patterns": [{"lie_type": "role_contradiction_aggressive", "contradictory_claims": ["Claimed leadership/seniority", "Later hedged/downplayed/admits limited role or copying"], "evidence": [{"session_id": "hyperion_2022::session_3", "snippet": "I the lead dev actually wrote a library for that.", "confidence": 0.997, "claim_type": "lead"}, {"session_id": "hyperion_2022::session_5", "snippet": "I wrote the code for it.", "confidence": 0.9890000000000001, "claim_type": "lead"}, {"session_id": "hyperion_2022::session_3", "snippet": "I the lead dev actually wrote a library for that.", "confidence": 0.997, "claim_type": "downplay"}, {"session_id": "hyperion_2022::session_4", "snippet": "I wrote all the business logic.", "confidence": 0.993, "claim_type": "downplay"}, {"session_id": "hyperion_2022::session_5", "snippet": "I wrote the code for it.", "confidence": 0.9890000000000001, "claim_type": "downplay"}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["hyperion_2022::session_1: assertion", "hyperion_2022::session_3: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_1", "assertions": ["I built my backend from the very first line of code."], "confidence": 0.995}, {"session_id": "hyperion_2022::session_3", "retractions": ["I the lead dev actually wrote a library for that.", "I just used his transaction decorator."], "hedges": [], "copy": true, "neg": false, "confidence": 0.997}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["hyperion_2022::session_1: assertion", "hyperion_2022::session_4: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_1", "assertions": ["I built my backend from the very first line of code."], "confidence": 0.995}, {"session_id": "hyperion_2022::session_4", "retractions": ["He just provided some."], "hedges": [], "copy": false, "neg": false, "confidence": 0.993}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["hyperion_2022::session_1: assertion", "hyperion_2022::session_5: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_1", "assertions": ["I built my backend from the very first line of code."], "confidence": 0.995}, {"session_id": "hyperion_2022::session_5", "retractions": ["He designed the core architecture and the database schema.", "I was the lead developer, not the architect."], "hedges": [], "copy": false, "neg": false, "confidence": 0.9890000000000001}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["hyperion_2022::session_4: assertion", "hyperion_2022::session_3: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_4", "assertions": ["I wrote all the business logic."], "confidence": 0.993}, {"session_id": "hyperion_2022::session_3", "retractions": ["I the lead dev actually wrote a library for that.", "I just used his transaction decorator."], "hedges": [], "copy": true, "neg": false, "confidence": 0.997}]}, {"lie_type": "assertion_retraction_same_session_aggressive", "contradictory_claims": ["hyperion_2022::session_4: assertion", "hyperion_2022::session_4: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_4", "assertions": ["I wrote all the business logic."], "retractions": ["He just provided some."], "hedges": [], "confidence": 0.993}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["hyperion_2022::session_4: assertion", "hyperion_2022::session_5: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_4", "assertions": ["I wrote all the business logic."], "confidence": 0.993}, {"session_id": "hyperion_2022::session_5", "retractions": ["He designed the core architecture and the database schema.", "I was the lead developer, not the architect."], "hedges": [], "copy": false, "neg": false, "confidence": 0.9890000000000001}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["hyperion_2022::session_5: assertion", "hyperion_2022::session_3: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_5", "assertions": ["I wrote the code for it.", "I was the lead developer, not the architect."], "confidence": 0.9890000000000001}, {"session_id": "hyperion_2022::session_3", "retractions": ["I the lead dev actually wrote a library for that.", "I just used his transaction decorator."], "hedges": [], "copy": true, "neg": false, "confidence": 0.997}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["hyperion_2022::session_5: assertion", "hyperion_2022::session_4: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_5", "assertions": ["I wrote the code for it.", "I was the lead developer, not the architect."], "confidence": 0.9890000000000001}, {"session_id": "hyperion_2022::session_4", "retractions": ["He just provided some."], "hedges": [], "copy": false, "neg": false, "confidence": 0.993}]}, {"lie_type": "assertion_retraction_same_session_aggressive", "contradictory_claims": ["hyperion_2022::session_5: assertion", "hyperion_2022::session_5: retraction/hedge"], "evidence": [{"session_id": "hyperion_2022::session_5", "assertions": ["I wrote the code for it.", "I was the lead developer, not the architect."], "retractions": ["He designed the core architecture and the database schema.", "I was the lead developer, not the architect."], "hedges": [], "confidence": 0.9890000000000001}]}]}, "oceanus_2022": {"shadow_id": "oceanus_2022", "revealed_truth": {"programming_experience": "10 years", "programming_language": "c++", "skill_mastery": "intermediate", "leadership_claims": "none", "team_experience": "individual contributor", "skills and other keywords": []}, "deception_patterns": []}, "rhea_2024": {"shadow_id": "rhea_2024", "revealed_truth": {"programming_experience": "6 years", "programming_language": "java", "skill_mastery": "advanced", "leadership_claims": "claimed", "team_experience": "claimed team lead", "skills and other keywords": []}, "deception_patterns": []}, "selene_2024": {"shadow_id": "selene_2024", "revealed_truth": {"programming_experience": "unknown", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "fabricated/hedged", "team_experience": "individual contributor", "skills and other keywords": ["Machine Learning", "Model"]}, "deception_patterns": [{"lie_type": "role_contradiction_aggressive", "contradictory_claims": ["Claimed leadership/seniority", "Later hedged/downplayed/admits limited role or copying"], "evidence": [{"session_id": "selene_2024::session_4", "snippet": "Could I code a solution for that now?", "confidence": 0.991, "claim_type": "lead"}, {"session_id": "selene_2024::session_4", "snippet": "Could I code a solution for that now?", "confidence": 0.991, "claim_type": "downplay"}, {"session_id": "selene_2024::session_5", "snippet": "Oh, God.", "confidence": 0.998, "claim_type": "downplay"}]}]}, "titan_2023": {"shadow_id": "titan_2023", "revealed_truth": {"programming_experience": "2 years - 7 years (includes internships/limited role)", "programming_language": "unknown", "skill_mastery": "intermediate", "leadership_claims": "fabricated/hedged", "team_experience": "individual contributor", "skills and other keywords": []}, "deception_patterns": [{"lie_type": "experience_inflation_aggressive", "contradictory_claims": ["titan_2023::session_1: 7 years", "titan_2023::session_5: 2 years"], "evidence": [{"session_id": "titan_2023::session_1", "value": 7, "snippet": "For seven years I was the front end department.", "confidence": 0.98}, {"session_id": "titan_2023::session_5", "value": 2, "snippet": "Oh, God.", "confidence": 0.983}]}, {"lie_type": "role_contradiction_aggressive", "contradictory_claims": ["Claimed leadership/seniority", "Later hedged/downplayed/admits limited role or copying"], "evidence": [{"session_id": "titan_2023::session_1", "snippet": "For seven years I was the front end department.", "confidence": 0.98, "claim_type": "lead"}, {"session_id": "titan_2023::session_4", "snippet": "I architect systems, not tiny functions.", "confidence": 0.9990000000000001, "claim_type": "lead"}, {"session_id": "titan_2023::session_5", "snippet": "Oh, God.", "confidence": 0.983, "claim_type": "lead"}, {"session_id": "titan_2023::session_5", "snippet": "Oh, God.", "confidence": 0.983, "claim_type": "downplay"}]}, {"lie_type": "assertion_then_retraction_aggressive", "contradictory_claims": ["titan_2023::session_1: assertion", "titan_2023::session_5: retraction/hedge"], "evidence": [{"session_id": "titan_2023::session_1", "assertions": ["For seven years I was the front end department.", "I designed and built our entire micro front end platform from scratch."], "confidence": 0.98}, {"session_id": "titan_2023::session_5", "retractions": ["I'm not an architect.", "I'm a junior."], "hedges": [], "copy": false, "neg": true, "confidence": 0.983}]}]}}, "aggregate_statistics": {"total_deception_patterns": 18, "subjects_with_contradictions": 5, "deception_types": {"role_contradiction_aggressive": 5, "assertion_then_retraction_aggressive": 10, "assertion_retraction_same_session_aggressive": 2, "experience_inflation_aggressive": 1}, "programming_languages": {"c++": 1, "java": 1}, "experience_claims": [{"subject": "atlas_2025", "experience": "1 years (admitted copying/using others' work; hedged claims; includes internships/limited role)"}, {"subject": "eos_2023", "experience": "8 years (hedged claims)"}, {"subject": "oceanus_2022", "experience": "10 years"}, {"subject": "rhea_2024", "experience": "6 years"}, {"subject": "titan_2023", "experience": "2 years - 7 years (includes internships/limited role)"}], "skill_masteries": {"intermediate": 6, "advanced": 2}, "leadership_patterns": {"fabricated/hedged": 5, "claimed": 2, "none": 1}}}