#!/usr/bin/env python3
"""
Truth Weaver - Main Orchestrator
================================

Complete pipeline for audio-to-text transcription and deception analysis.

Usage:
    python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio" --output-dir output
    python truth_weaver.py --batch --audio-dir "audio copy/audio" --output-dir output
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List, Optional
import re

# Add src and scripts to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

try:
    from run_assemblyai import transcribe_file
    from submission_scroll import build_submission_scroll, parse_master_transcript
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure src/asr_runner.py and scripts/submission_scroll.py are available")
    sys.exit(1)


class TruthWeaver:
    """Main Truth Weaver orchestrator class."""
    
    def __init__(self, audio_dir: Path, output_dir: Path, backend: str = "assemblyai"):
        self.audio_dir = Path(audio_dir)
        self.output_dir = Path(output_dir)
        self.backend = backend
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def find_subject_sessions(self, subject: str) -> List[Path]:
        """Find all audio files for a given subject."""
        pattern = f"{subject}_*.mp3"
        sessions = list(self.audio_dir.glob(pattern))
        if not sessions:
            # Try alternative patterns
            pattern = f"{subject}*.mp3"
            sessions = list(self.audio_dir.glob(pattern))
        
        # Sort by session number
        def extract_session_num(path: Path) -> int:
            match = re.search(r'_(\d+)\.mp3$', path.name)
            return int(match.group(1)) if match else 0
            
        return sorted(sessions, key=extract_session_num)
    
    def transcribe_session(self, audio_file: Path) -> Dict[str, Any]:
        """Transcribe a single audio session."""
        print(f"Transcribing {audio_file.name}...")
        
        try:
            result = transcribe_file(str(audio_file), backend=self.backend)
            return result
        except Exception as e:
            print(f"Error transcribing {audio_file}: {e}")
            return {
                "raw_text": f"[TRANSCRIPTION_ERROR: {str(e)}]",
                "segments": [],
                "error": str(e)
            }
    
    def create_master_transcript(self, subject: str, session_results: List[Dict[str, Any]], 
                               session_files: List[Path]) -> str:
        """Create a master transcript from all sessions."""
        master_lines = []
        
        for i, (result, audio_file) in enumerate(zip(session_results, session_files), 1):
            session_title = f"Session {i}"
            
            # Add session header
            master_lines.append(f"SESSION {i}: {session_title}")
            master_lines.append("=" * 50)
            master_lines.append("")
            
            # Add full text
            raw_text = result.get("raw_text", "")
            if raw_text and not raw_text.startswith("["):
                master_lines.append("FULL TEXT:")
                master_lines.append(raw_text)
                master_lines.append("")
                
                # Add timestamped breakdown if available
                segments = result.get("segments", [])
                if segments and len(segments) > 1:
                    master_lines.append("TIMESTAMPED BREAKDOWN:")
                    for seg in segments:
                        start = seg.get("start", 0)
                        end = seg.get("end", 0)
                        text = seg.get("text", "")
                        conf = seg.get("confidence", 0)
                        if text.strip():
                            master_lines.append(f"[{start:.1f}s - {end:.1f}s] {text}")
                    master_lines.append("")
                
                # Add overall confidence if available
                if segments:
                    avg_conf = sum(s.get("confidence", 0) for s in segments) / len(segments)
                    master_lines.append(f"Overall Confidence: {avg_conf*100:.1f}%")
                    master_lines.append("")
            else:
                master_lines.append(f"TRANSCRIPTION ISSUE: {raw_text}")
                master_lines.append("")
            
            master_lines.append("")
        
        return "\n".join(master_lines)
    
    def process_subject(self, subject: str) -> Dict[str, Any]:
        """Process a complete subject (all sessions)."""
        print(f"\n=== Processing Subject: {subject} ===")
        
        # Find all session files
        session_files = self.find_subject_sessions(subject)
        if not session_files:
            print(f"No audio files found for subject: {subject}")
            return {}
        
        print(f"Found {len(session_files)} sessions: {[f.name for f in session_files]}")
        
        # Transcribe all sessions
        session_results = []
        for audio_file in session_files:
            result = self.transcribe_session(audio_file)
            session_results.append(result)
        
        # Create master transcript
        master_transcript = self.create_master_transcript(subject, session_results, session_files)
        
        # Save master transcript
        master_path = self.output_dir / f"{subject}_MASTER_TRANSCRIPT.txt"
        master_path.write_text(master_transcript, encoding="utf-8")
        print(f"Saved master transcript: {master_path}")
        
        # Generate submission scroll (JSON analysis)
        try:
            result = build_submission_scroll(master_path, subject, self.output_dir)
            print(f"Generated Truth Weaver analysis for {subject}")
            return result
        except Exception as e:
            print(f"Error generating submission scroll for {subject}: {e}")
            return {}
    
    def process_batch(self) -> Dict[str, Dict[str, Any]]:
        """Process all subjects found in audio directory."""
        print("=== BATCH PROCESSING MODE ===")
        
        # Find all unique subjects
        audio_files = list(self.audio_dir.glob("*.mp3"))
        subjects = set()
        
        for audio_file in audio_files:
            # Extract subject name (everything before the last underscore and number)
            match = re.match(r'^(.+?)_\d+\.mp3$', audio_file.name)
            if match:
                subjects.add(match.group(1))
        
        print(f"Found subjects: {sorted(subjects)}")
        
        results = {}
        for subject in sorted(subjects):
            try:
                result = self.process_subject(subject)
                if result:
                    results[subject] = result
            except Exception as e:
                print(f"Error processing {subject}: {e}")
                results[subject] = {"error": str(e)}
        
        return results


def main():
    parser = argparse.ArgumentParser(
        description="Truth Weaver - Audio transcription and deception analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process single subject
  python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio"
  
  # Process all subjects in batch
  python truth_weaver.py --batch --audio-dir "audio copy/audio"
  
  # Use stub backend for testing
  python truth_weaver.py --subject atlas_2025 --audio-dir "audio copy/audio" --backend stub
        """
    )
    
    parser.add_argument("--subject", help="Subject to process (e.g., atlas_2025)")
    parser.add_argument("--batch", action="store_true", help="Process all subjects")
    parser.add_argument("--audio-dir", required=True, help="Directory containing audio files")
    parser.add_argument("--output-dir", default="output", help="Output directory")
    parser.add_argument("--backend", choices=["assemblyai", "stub"], default="assemblyai",
                       help="Transcription backend")
    
    args = parser.parse_args()
    
    if not args.subject and not args.batch:
        parser.error("Must specify either --subject or --batch")
    
    if args.subject and args.batch:
        parser.error("Cannot specify both --subject and --batch")
    
    # Initialize Truth Weaver
    weaver = TruthWeaver(args.audio_dir, args.output_dir, args.backend)
    
    try:
        if args.batch:
            results = weaver.process_batch()
            print(f"\n=== BATCH PROCESSING COMPLETE ===")
            print(f"Processed {len(results)} subjects")
            for subject, result in results.items():
                if "error" in result:
                    print(f"  {subject}: ERROR - {result['error']}")
                else:
                    print(f"  {subject}: SUCCESS")
        else:
            result = weaver.process_subject(args.subject)
            if result:
                print(f"\n=== PROCESSING COMPLETE ===")
                print(f"Subject: {args.subject}")
                print(f"Output files saved to: {args.output_dir}")
            else:
                print(f"Failed to process subject: {args.subject}")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
