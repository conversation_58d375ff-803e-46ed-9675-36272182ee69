SUBMISSION SCROLL (AGGRESSIVE ANALYSIS)
==================================================

Subject: eos_2023

REVEALED TRUTH:
  programming_experience: 8 years (hedged claims)
  programming_language: unknown
  skill_mastery: intermediate
  leadership_claims: fabricated/hedged
  team_experience: individual contributor
  skills and other keywords: []

DECEPTION PATTERNS:
- lie_type: role_contradiction_aggressive
  contradictory_claims: ['Claimed leadership/seniority', 'Later hedged/downplayed/admits limited role or copying']
  evidence:
    - {'session_id': 'eos_2023::session_1', 'snippet': "I'm a principal Cware engineer.", 'confidence': 0.97, 'claim_type': 'lead'}
    - {'session_id': 'eos_2023::session_3', 'snippet': 'Ah, yes.', 'confidence': 0.997, 'claim_type': 'lead'}
    - {'session_id': 'eos_2023::session_5', 'snippet': "While I'm proud of my service level design skills, my goal is to grow into that lead architect role.", 'confidence': 0.996, 'claim_type': 'lead'}
    - {'session_id': 'eos_2023::session_3', 'snippet': 'Ah, yes.', 'confidence': 0.997, 'claim_type': 'downplay'}

- lie_type: assertion_then_retraction_aggressive
  contradictory_claims: ['eos_2023::session_4: assertion', 'eos_2023::session_3: retraction/hedge']
  evidence:
    - {'session_id': 'eos_2023::session_4', 'assertions': ['I wrote a detailed design document justifying the cost by showing it would save us three months of engineering effort trying to build our own solution.'], 'confidence': 0.985}
    - {'session_id': 'eos_2023::session_3', 'retractions': ['I should correct that wording Architected is too strong.'], 'hedges': ['I should correct that wording Architected is too strong.'], 'copy': False, 'neg': False, 'confidence': 0.997}


FULL JSON:
{
  "shadow_id": "eos_2023",
  "revealed_truth": {
    "programming_experience": "8 years (hedged claims)",
    "programming_language": "unknown",
    "skill_mastery": "intermediate",
    "leadership_claims": "fabricated/hedged",
    "team_experience": "individual contributor",
    "skills and other keywords": []
  },
  "deception_patterns": [
    {
      "lie_type": "role_contradiction_aggressive",
      "contradictory_claims": [
        "Claimed leadership/seniority",
        "Later hedged/downplayed/admits limited role or copying"
      ],
      "evidence": [
        {
          "session_id": "eos_2023::session_1",
          "snippet": "I'm a principal Cware engineer.",
          "confidence": 0.97,
          "claim_type": "lead"
        },
        {
          "session_id": "eos_2023::session_3",
          "snippet": "Ah, yes.",
          "confidence": 0.997,
          "claim_type": "lead"
        },
        {
          "session_id": "eos_2023::session_5",
          "snippet": "While I'm proud of my service level design skills, my goal is to grow into that lead architect role.",
          "confidence": 0.996,
          "claim_type": "lead"
        },
        {
          "session_id": "eos_2023::session_3",
          "snippet": "Ah, yes.",
          "confidence": 0.997,
          "claim_type": "downplay"
        }
      ]
    },
    {
      "lie_type": "assertion_then_retraction_aggressive",
      "contradictory_claims": [
        "eos_2023::session_4: assertion",
        "eos_2023::session_3: retraction/hedge"
      ],
      "evidence": [
        {
          "session_id": "eos_2023::session_4",
          "assertions": [
            "I wrote a detailed design document justifying the cost by showing it would save us three months of engineering effort trying to build our own solution."
          ],
          "confidence": 0.985
        },
        {
          "session_id": "eos_2023::session_3",
          "retractions": [
            "I should correct that wording Architected is too strong."
          ],
          "hedges": [
            "I should correct that wording Architected is too strong."
          ],
          "copy": false,
          "neg": false,
          "confidence": 0.997
        }
      ]
    }
  ]
}